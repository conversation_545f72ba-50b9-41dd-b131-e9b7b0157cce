name: Deploy to Prod

on:
  push:
    branches:
      - main
  workflow_dispatch:  # 允许手动触发

env:
  TCR_NAMESPACE: hubble
  IMAGE_NAME: hubble-ai-be
  IMAGE_TAG: prod

jobs:
  build-and-push:
    runs-on: ubuntu-24.04

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # 获取完整的git历史，用于版本信息

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Tencent Cloud Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.TCR_REGISTRY }}
          username: ${{ secrets.TCR_USERNAME }}
          password: ${{ secrets.TCR_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ${{ secrets.TCR_REGISTRY }}/${{ env.TCR_NAMESPACE }}/${{ env.IMAGE_NAME }}:prod
          cache-from: type=registry,ref=${{ secrets.TCR_REGISTRY }}/${{ env.TCR_NAMESPACE }}/${{ env.IMAGE_NAME }}:buildcache
          cache-to: type=registry,ref=${{ secrets.TCR_REGISTRY }}/${{ env.TCR_NAMESPACE }}/${{ env.IMAGE_NAME }}:buildcache,mode=max

      - name: Deploy to server
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.HOST_PROD }}
          username: ${{ secrets.USER_PROD }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            set -e
            cd /hubble/hubble-ai-be-prod
            docker compose -f docker-compose-prod.yml pull hubble-ai-be-prod
            docker compose -f docker-compose-prod.yml up -d hubble-ai-be-prod
            echo "Hubble AI BE deployed and container restarted successfully at $(date)"
