FROM node:22-alpine AS base

FROM base AS builder

RUN apk add --no-cache gcompat
WORKDIR /app

COPY package.json pnpm-lock.yaml tsconfig.json langgraph.json ./
COPY src ./src

RUN npm install -g pnpm && \
    pnpm install && \
    pnpm run build && \
    pnpm prune --production

FROM base AS runner
WORKDIR /app

# Add build argument for NODE_ENV
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 hono

COPY --from=builder --chown=hono:nodejs /app/node_modules /app/node_modules
COPY --from=builder --chown=hono:nodejs /app/dist /app/dist
COPY --from=builder --chown=hono:nodejs /app/package.json /app/package.json
COPY --from=builder --chown=hono:nodejs /app/langgraph.json /app/langgraph.json

USER hono
EXPOSE 3000

CMD ["node", "--env-file=.env", "/app/dist/server/app.js"]