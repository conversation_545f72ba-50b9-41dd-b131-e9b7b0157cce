{"name": "example-graph", "version": "0.0.1", "description": "A starter template for creating a LangGraph workflow.", "main": "my_app/graph.ts", "author": "Your Name", "license": "MIT", "private": true, "type": "module", "scripts": {"dev": "npx @langchain/langgraph-cli@latest dev", "build": "tsc", "clean": "rm -rf dist", "start": "npx tsx src/server/app.ts", "start:dev": "npx tsx --watch src/server/app.ts", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js --testPathPattern=\\.test\\.ts$ --testPathIgnorePatterns=\\.int\\.test\\.ts$", "test:int": "node --experimental-vm-modules node_modules/jest/bin/jest.js --testPathPattern=\\.int\\.test\\.ts$", "format": "prettier --write .", "lint": "eslint src", "format:check": "prettier --check .", "lint:langgraph-json": "node scripts/checkLanggraphPaths.js", "lint:all": "yarn lint & yarn lint:langgraph-json & yarn format:check", "test:all": "yarn test && yarn test:int && yarn lint:langgraph", "test:quick": "npx tsx src/test-simple.ts quick", "test:simple": "npx tsx src/test-simple.ts simple", "test:complex": "npx tsx src/test-simple.ts complex", "test:text2sql": "npx tsx src/test-simple.ts all", "example": "npx tsx src/examples/text2sqlPlanExample.ts", "test:hybrid-rag": "npx tsx src/examples/ragHybridTest.ts", "test:rag-tool": "npx tsx src/examples/ragToolTest.ts", "test:direct-sql": "npx tsx src/examples/directSQLTest.ts", "debug:workflow": "npx tsx src/examples/debugWorkflowTest.ts", "test:structured-steps": "npx tsx src/examples/structuredStepTest.ts", "test:raw-data": "npx tsx src/examples/simpleDataArrayTest.ts", "test:chart-agent": "npx tsx src/examples/chartAgentSimpleTest.ts", "test:chart-service": "npx tsx src/examples/chartServiceTest.ts", "start:prod": "NODE_ENV=production node --env-file=.env dist/server/app.js"}, "dependencies": {"@clickhouse/client": "^1.11.2", "@e2b/code-interpreter": "^1.5.1", "@hono/node-server": "^1.14.4", "@hono/swagger-ui": "^0.5.2", "@hono/zod-openapi": "^0.19.9", "@langchain/community": "^0.3.47", "@langchain/core": "^0.3.57", "@langchain/google-genai": "^0.2.14", "@langchain/langgraph": "^0.3.0", "@langchain/langgraph-checkpoint-postgres": "^0.0.5", "@langchain/openai": "^0.3.17", "@scalar/hono-api-reference": "^0.9.7", "dotenv": "^16.6.1", "hono": "^4.8.3", "langchain": "^0.3.29", "node-fetch": "2", "node-pg-migrate": "^8.0.3", "pg": "^8.16.3", "zod": "^3.25.70"}, "devDependencies": {"@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.9.1", "@tsconfig/recommended": "^1.0.7", "@types/cors": "^2.8.17", "@types/jest": "^29.5.0", "@types/node": "^24.0.7", "@types/pg": "^8.15.4", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.7.0", "prettier": "^3.3.3", "ts-jest": "^29.1.0", "typescript": "^5.3.3"}}