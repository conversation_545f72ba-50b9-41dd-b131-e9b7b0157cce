<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Text2SQL & Chart Generation API Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script
      defer
      src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"
    ></script>
  </head>

  <body class="bg-gradient-to-br from-indigo-400 to-purple-500 font-sans">
    <div
      x-data="text2sqlApp()"
      class="container mx-auto flex h-[95vh] flex-col rounded-2xl bg-white/95 p-5 shadow-2xl"
    >
      <header class="mb-4 shrink-0 text-center">
        <h1
          class="bg-gradient-to-r from-indigo-600 to-purple-700 bg-clip-text text-4xl font-bold text-transparent"
        >
          Text2SQL & Chart Generation API Test
        </h1>
        <p class="mt-2 text-lg text-gray-600">
          A simple UI to test the streaming Text2SQL and Chart Generation API.
        </p>
      </header>

      <div class="mb-4 shrink-0">
        <div class="mb-2 font-semibold text-gray-700">
          Example Queries (Click to use):
        </div>
        <div>
          <template x-for="example in exampleQueries" :key="example">
            <button
              @click="setQuery(example)"
              class="mb-2 mr-2 inline-block rounded-full bg-gray-200 px-4 py-2 text-sm text-gray-800 transition hover:-translate-y-0.5 hover:bg-gray-300 hover:shadow-md"
              x-text="example"
            ></button>
          </template>
        </div>
      </div>

      <main class="flex min-h-0 flex-1 gap-5">
        <!-- Query Section -->
        <section class="flex flex-1 flex-col rounded-xl border border-gray-200 bg-gray-50 p-6">
          <div class="form-group mb-4">
            <label for="queryInput" class="mb-2 block text-lg font-semibold text-gray-800">
              Enter your natural language query:
            </label>
            <textarea
              id="queryInput"
              x-model="query"
              placeholder="e.g., Show me the top 10 token trades by volume today"
              rows="4"
              class="w-full rounded-lg border-2 border-gray-200 p-4 transition focus:border-transparent focus:outline-none focus:ring-2 focus:ring-indigo-500"
            ></textarea>
          </div>
          <div class="button-group flex flex-wrap gap-4">
            <button
              @click="executeQuery()"
              :disabled="isProcessing"
              class="rounded-lg bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-3 font-semibold text-white shadow-md transition hover:-translate-y-0.5 hover:shadow-lg disabled:cursor-not-allowed disabled:bg-gray-400 disabled:from-gray-400 disabled:shadow-none"
            >
              🚀 Send Query
            </button>
            <button
              @click="executeChartQuery()"
              :disabled="isProcessing"
              class="rounded-lg bg-gradient-to-r from-pink-500 to-orange-500 px-6 py-3 font-semibold text-white shadow-md transition hover:-translate-y-0.5 hover:shadow-lg disabled:cursor-not-allowed disabled:bg-gray-400 disabled:from-gray-400 disabled:shadow-none"
            >
              📊 Generate Chart
            </button>
            <button
              @click="clearResults()"
              :disabled="isProcessing"
              class="rounded-lg bg-gray-500 px-6 py-3 font-semibold text-white shadow-md transition hover:-translate-y-0.5 hover:shadow-lg disabled:cursor-not-allowed disabled:bg-gray-400 disabled:shadow-none"
            >
              🗑️ Clear
            </button>
          </div>
        </section>

        <!-- Results Section -->
        <section class="flex flex-1 flex-col rounded-xl border border-gray-200 bg-gray-50 p-6">
          <header class="mb-4 flex items-center justify-between">
            <h2 class="text-xl font-bold text-gray-800">Results</h2>
            <div
              :class="{
                'bg-gray-200 text-gray-600': status === 'Idle',
                'bg-blue-100 text-blue-800': status === 'Processing',
                'bg-green-100 text-green-800': status === 'Completed',
                'bg-red-100 text-red-800': status === 'Error'
              }"
              class="flex items-center rounded-full px-4 py-1.5 text-sm font-semibold transition"
            >
              <template x-if="status === 'Processing'">
                <div
                  class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-blue-800 border-t-transparent"
                ></div>
              </template>
              <span x-text="status"></span>
            </div>
          </header>
          <div
            id="output"
            x-ref="output"
            class="flex-1 overflow-y-auto rounded-lg border border-gray-300 bg-white p-4 font-mono text-sm"
          >
            <template x-if="messages.length === 0">
              <div class="text-gray-500">Ready...</div>
            </template>
            <template x-for="msg in messages" :key="msg.id">
              <div
                :class="{
                  'border-blue-500 bg-blue-50': msg.type === 'result',
                  'border-red-500 bg-red-50': msg.type === 'error',
                  'border-sky-500 bg-sky-50': msg.type === 'token-stream',
                  'border-orange-500 bg-orange-50': msg.type === 'chart'
                }"
                class="mb-3 rounded-lg border-l-4 p-3"
              >
                <!-- Token Stream Rendering -->
                <template x-if="msg.type === 'token-stream'">
                  <div>
                    <div class="mb-2 flex items-center font-sans font-semibold text-sky-800">
                      🤖 LLM Response
                      <span x-show="msg.streaming" class="ml-2 animate-pulse font-bold">▊</span>
                      <span x-show="!msg.streaming" class="ml-2">✅</span>
                    </div>
                    <div
                      class="prose prose-sm max-w-none rounded-lg border border-gray-200 bg-white p-3 font-sans"
                      x-html="renderMarkdown(msg.content)"
                    ></div>
                    <div class="pt-1 text-right text-xs text-gray-500" x-text="msg.timestamp"></div>
                  </div>
                </template>

                <!-- Chart Rendering -->
                <template x-if="msg.type === 'chart'">
                  <div>
                    <div class="mb-2 font-sans font-semibold text-orange-800" x-text="msg.title"></div>
                    <div class="chart-preview rounded-lg border bg-white p-2">
                        <iframe :srcdoc="msg.content" class="h-[400px] w-full rounded-md border-none"></iframe>
                    </div>
                     <div class="mt-2 text-center">
                        <button @click="openChartInNewTab(msg.content)" class="rounded-md bg-blue-500 px-4 py-2 text-sm font-semibold text-white hover:bg-blue-600">
                            Open in New Tab
                        </button>
                    </div>
                    <div class="pt-1 text-right text-xs text-gray-500" x-text="msg.timestamp"></div>
                  </div>
                </template>

                <!-- Simple Message (Result/Error) -->
                <template x-if="msg.type === 'result' || msg.type === 'error'">
                   <div>
                      <div class="font-sans" x-html="renderMarkdown(msg.content)"></div>
                      <div class="pt-1 text-right text-xs text-gray-500" x-text="msg.timestamp"></div>
                   </div>
                </template>
              </div>
            </template>
          </div>
        </section>
      </main>
    </div>

    <script>
      function text2sqlApp() {
        return {
          // --- STATE ---
          query: "",
          status: "Idle", // Idle, Processing, Completed, Error
          messages: [], // { id, type, content, timestamp, ... }
          isProcessing: false,
          currentStreamId: null,
          exampleQueries: [
            "Show me the top 10 token trades by volume today",
            "What are the most active trading pairs in the last 24 hours?",
            "Show me a chart of trading volume over time for the past week",
          ],

          // --- LIFECYCLE & HELPERS ---
          init() {
            // Auto-scroll messages
            this.$watch("messages", () => {
              this.$nextTick(() => {
                this.$refs.output.scrollTop = this.$refs.output.scrollHeight;
              });
            });
          },
          setQuery(example) {
            this.query = example;
          },
          clearResults() {
            this.messages = [];
            this.status = "Idle";
            this.isProcessing = false;
            // Note: EventSource connections are not explicitly closed here,
            // but they will be terminated when the browser navigates away or
            // a new query starts a new connection.
          },
          formatTimestamp() {
            return new Date().toLocaleTimeString();
          },
          openChartInNewTab(htmlCode) {
            const newWindow = window.open('', '_blank');
            newWindow.document.write(htmlCode);
            newWindow.document.close();
          },

          // --- API CALLS ---
          async startStream(endpoint, body) {
            if (this.isProcessing) return;
            if (!this.query.trim()) {
              alert("Please enter a query");
              return;
            }

            this.isProcessing = true;
            this.status = "Processing";
            this.messages = [];
            this.currentStreamId = `stream-${Date.now()}`;

            try {
              const response = await fetch(endpoint, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(body),
              });

              if (!response.body) throw new Error("ReadableStream not supported.");

              const reader = response.body.getReader();
              const decoder = new TextDecoder();

              while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    this.status = "Completed";
                    // Finalize any open streams
                    const openStream = this.messages.find(m => m.streaming);
                    if (openStream) openStream.streaming = false;
                    break;
                }

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split("\n");

                for (const line of lines) {
                  if (line.startsWith("data: ")) {
                    try {
                      const data = JSON.parse(line.slice(6));
                      if (endpoint.includes('generate-chart')) {
                          this.handleChartStreamEvent(data);
                      } else {
                          this.handleStreamEvent(data);
                      }
                    } catch (e) {
                      console.log("JSON Parse Error:", e, line);
                    }
                  }
                }
              }
            } catch (error) {
              this.status = "Error";
              this.messages.push({
                id: Date.now(),
                type: "error",
                content: `❌ Network or stream error: ${error.message}`,
                timestamp: this.formatTimestamp(),
              });
            } finally {
              this.isProcessing = false;
            }
          },

          executeQuery() {
            this.startStream("/agent/api/v1/text2sql", {
              query: this.query,
              stream: true,
            });
          },

          executeChartQuery() {
            this.startStream("/agent/api/v1/generate-chart", {
              query: this.query,
              stream: true,
            });
          },

          // --- EVENT HANDLERS ---
          handleStreamEvent(data) {
            switch (data.type) {
              case "llm_start": {
                this.currentStreamId = data.runId;
                this.messages.push({
                  id: data.runId,
                  type: "token-stream",
                  content: "",
                  streaming: true,
                  timestamp: this.formatTimestamp(),
                });
                break;
              }
              case "token": {
                const msg = this.messages.find((m) => m.id === data.runId);
                if (msg) msg.content += data.token;
                break;
              }
              case "llm_end": {
                const msg = this.messages.find((m) => m.id === data.runId);
                if (msg) msg.streaming = false;
                this.currentStreamId = null;
                break;
              }
              case "result": {
                this.messages.push({
                  id: Date.now(),
                  type: "result",
                  content: `\n${JSON.stringify(data.data, null, 2)}\n`,
                  timestamp: this.formatTimestamp(),
                });
                break;
              }
              case "error": {
                this.status = "Error";
                this.messages.push({
                  id: Date.now(),
                  type: "error",
                  content: `❌ Error: ${data.error}`,
                  timestamp: this.formatTimestamp(),
                });
                break;
              }
              case "complete":
                this.status = "Completed";
                break;
            }
          },

          handleChartStreamEvent(data) {
             switch (data.type) {
                case "llm_start":
                case "token":
                case "llm_end":
                    // Route generic LLM events to the standard handler
                    this.handleStreamEvent(data);
                    break;
                case "chart_generation_complete":
                case "chart_complete": {
                    if (data.htmlCode) {
                        this.messages.push({
                            id: Date.now(),
                            type: 'chart',
                            title: '📊 Chart Generated Successfully!',
                            content: data.htmlCode,
                            timestamp: this.formatTimestamp(),
                        });
                    }
                    this.status = 'Completed';
                    break;
                }
                case "chart_sql_complete": {
                     this.messages.push({
                        id: Date.now(),
                        type: 'result',
                        content: `✅ SQL query completed: Found **${data.rowCount}** rows. Now generating chart...\n\n**Query Results:**\n\n${JSON.stringify(data.data, null, 2)}\n`,
                        timestamp: this.formatTimestamp(),
                    });
                    break;
                }
                 case "chart_start": {
                    this.messages.push({
                        id: Date.now(),
                        type: 'result',
                        content: `📊 Starting chart generation for: "${data.query}"`,
                        timestamp: this.formatTimestamp(),
                    });
                    break;
                }
                case "chart_error": {
                    this.status = "Error";
                    this.messages.push({
                      id: Date.now(),
                      type: "error",
                      content: `❌ Chart Error: ${data.error}`,
                      timestamp: this.formatTimestamp(),
                    });
                    break;
                }
             }
          },

          // --- MARKDOWN RENDERING ---
          renderMarkdown(text) {
             if (!text) return '';
              text = text.replace(/</g, "&lt;").replace(/>/g, "&gt;");
              text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
              text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
              text = text.replace(/`([^`]+)`/g, '<code class="bg-gray-200 text-sm rounded px-1 py-0.5">$1</code>');
              text = text.replace(/```([\s\S]*?)```/g, (match, p1) => {
                  const code = p1.trim().replace(/</g, "&lt;").replace(/>/g, "&gt;");
                  return `<pre class="bg-gray-100 text-sm rounded p-3 my-2 overflow-x-auto"><code>${code}</code></pre>`;
              });
              
              const tableRegex = /(\|.*\|(?:\r?\n|\r)?)+/g;
              text = text.replace(tableRegex, (match) => {
                  const rows = match.trim().split(/\r?\n|\r/);
                  if (rows.length < 2) return match; // Not a valid table

                  // Skip separator line
                  const headerCells = rows[0].split('|').slice(1, -1).map(c => c.trim());
                  const bodyRows = rows.slice(2).map(r => r.split('|').slice(1, -1).map(c => c.trim()));
                  
                  let table = '<div class="overflow-x-auto my-2"><table class="w-full border-collapse border border-gray-300 text-left text-xs">';
                  table += '<thead><tr class="bg-gray-100">';
                  headerCells.forEach(c => table += `<th class="border border-gray-300 p-2 font-semibold">${c}</th>`);
                  table += '</tr></thead><tbody>';
                  bodyRows.forEach(row => {
                      table += '<tr>';
                      row.forEach(cell => table += `<td class="border border-gray-300 p-2">${cell}</td>`);
                      table += '</tr>';
                  });
                  table += '</tbody></table></div>';
                  return table;
              });

              return text.replace(/\n/g, '<br>');
          }
        };
      }
    </script>
  </body>
</html>
