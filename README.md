# Hubble AI - LangGraph Text2SQL Agent

[![CI](https://github.com/langchain-ai/new-langgraphjs-project/actions/workflows/unit-tests.yml/badge.svg)](https://github.com/langchain-ai/new-langgraphjs-project/actions/workflows/unit-tests.yml)
[![Integration Tests](https://github.com/langchain-ai/new-langgraphjs-project/actions/workflows/integration-tests.yml/badge.svg)](https://github.com/langchain-ai/new-langgraphjs-project/actions/workflows/integration-tests.yml)

This project is a Text-to-SQL agent built with [LangGraph.js](https://github.com/langchain-ai/langgraphjs). It is designed to generate SQL queries from natural language through a conversational interface, leveraging the power of Large Language Models (LLMs) and a Plan-and-Execute strategy.

<p align="center">
  <img src="./static/studo.jpg" alt="Graph view in LangGraph Studio UI" width="75%">
</p>

## Features

This Text2SQL agent implements a sophisticated workflow for robust and accurate query generation:

- **Plan-and-Execute Model**: The agent first creates a plan to tackle the user's request, then executes the steps, allowing for more complex, multi-step queries.
- **RAG-Enhanced SQL Generation**: It uses Retrieval-Augmented Generation (RAG) to fetch relevant database schema information and examples, leading to more accurate SQL queries.
- **Error Recovery**: The graph includes built-in recovery mechanisms to handle SQL errors, analyze them, and attempt to self-correct.
- **Streaming Responses**: The entire process, from planning to final result, is streamed to the client via Server-Sent Events (SSE) for a real-time user experience.

## Project Structure

The repository is organized to separate concerns, making it easier to navigate and maintain.

```
hubble-ai-be/
├── src/                          # Source code
│   ├── agent/                    # Core LangGraph Agent
│   │   ├── nodes/                # Graph nodes (Planner, Executor, RAG, etc.)
│   │   ├── state.ts              # State definition
│   │   └── text2sqlPlanGraph.ts  # Main graph definition
│   ├── server/                   # Hono web server
│   │   └── app.ts                # Main server application (Hono + SSE)
│   ├── config/                   # Configuration files
│   ├── services/                 # Business logic services (LLM, RAG)
│   ├── tools/                    # Tools for the LangGraph agent
│   ├── prompts/                  # LLM prompt templates
│   └── utils/                    # Shared utility functions
├── tests/                        # Tests
├── public/                       # Static assets for frontend
├── package.json                  # Project dependencies and scripts
└── .cursorrules                  # Project rules for AI assistant
```

## Getting Started

Follow these steps to get the project running locally.

### Prerequisites

- Node.js (v23 or higher)
- pnpm

### 1. Install Dependencies

Clone the repository and install the required packages.

```bash
pnpm install
```

### 2. Environment Variables

Create a `.env` file in the root of the project. You may be able to copy an existing `.env.example` file if one is available.

Your `.env` file should contain the following variables:

```env
# LLM API Keys
OPENAI_API_KEY=your_openai_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key

# ClickHouse (For data queries)
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_USERNAME=default
CLICKHOUSE_PASSWORD=
CLICKHOUSE_DATABASE=hubble

# Server Configuration
PORT=3000
NODE_ENV=development
```

### 3. Run the Application

Start the development server. The server will be available at `http://localhost:3000`.

```bash
pnpm run start:dev
```

The server exposes a main endpoint at `/api/chat/text-to-sql-stream` for handling Text2SQL requests. You can interact with it using the simple frontend served at the root URL.

## Available Scripts

- `pnpm run start:dev`: Starts the development server with hot-reloading.
- `pnpm run build`: Compiles the TypeScript code to JavaScript.
- `pnpm run test`: Runs unit tests.
- `pnpm run test:int`: Runs integration tests.
- `pnpm run format`: Formats the code using Prettier.
- `pnpm run lint`: Lints the code using ESLint.

## Development with LangGraph Studio

This project is fully compatible with [LangGraph Studio](https://langchain-ai.github.io/langgraph/concepts/langgraph_studio/) for visual debugging. To use it, start the server with the LangGraph CLI:

```bash
npx @langchain/langgraph-cli dev
```

This will provide a link to a local instance of LangGraph Studio where you can inspect the graph's execution, view the state at each step, and debug your application interactively.

<!--
Configuration auto-generated by `langgraph template lock`. DO NOT EDIT MANUALLY.
{
  "config_schemas": {
    "agent": {
      "type": "object",
      "properties": {}
    }
  }
}
-->
