# Hubble AI Backend - LangGraph Text2SQL Agent 项目规则

## 项目概述

这是一个基于 LangGraphJS 的 Text2SQL Agent 项目，支持对话式 SQL 查询生成。

## 遵循 KISS 原则
保持代码简单、清晰、易维护。避免过度复杂的架构和不必要的抽象。

## 项目结构

```
hubble-ai-be/
├── src/                          # 源代码目录
│   ├── server/                   # 服务器相关代码
│   │   └── app.ts               # 主服务器应用 (Hono + SSE)
│   ├── agent/                    # LangGraph Agent 核心
│   │   ├── text2sqlPlanGraph.ts # Text2SQL 规划执行图
│   │   ├── state.ts             # 状态定义
│   │   ├── types.ts             # 类型定义
│   │   ├── utils.ts             # 工具函数
│   │   ├── errors.ts            # 错误处理
│   │   └── nodes/               # LangGraph 节点
│   │       ├── plannerNode.ts   # 规划节点
│   │       ├── executorNode.ts  # 执行节点
│   │       ├── ragNode.ts       # RAG 节点
│   │       ├── recoveryNode.ts  # 恢复节点
│   │       ├── completionNode.ts # 完成节点
│   │       ├── analysisStep.ts  # 分析步骤
│   │       └── sqlStep.ts       # SQL 步骤
│   ├── config/                   # 配置文件
│   │   ├── text2sql.ts          # Text2SQL 配置
│   │   ├── database.ts          # 数据库模式定义
│   │   ├── schemaFormatter.ts   # 模式格式化
│   │   └── README.md            # 配置说明
│   ├── services/                 # 业务服务层
│   │   ├── llmService.ts        # LLM 服务
│   │   ├── ragService.ts        # RAG 服务
│   │   └── formatService.ts     # 格式化服务
│   ├── tools/                    # LangGraph 工具
│   │   └── ragTool.ts           # RAG 工具
│   ├── prompts/                  # 提示词模板
│   │   ├── text2sqlPrompt.ts    # Text2SQL 提示词
│   │   └── planExecutePrompts.ts # 规划执行提示词
│   ├── utils/                    # 通用工具
│   │   ├── index.ts             # 工具导出
│   │   └── blockchainTerminology.ts # 区块链术语
│   ├── examples/                 # 示例代码
│   │   ├── text2sqlPlanExample.ts      # 基础示例
│   │   ├── ragHybridTest.ts            # RAG 混合测试
│   │   └── ...                         # 其他示例
│   ├── index.ts                  # 主入口文件
│   └── test-simple.ts           # 简单测试
├── tests/                        # 测试文件
│   ├── agent.test.ts            # Agent 测试
│   └── graph.int.test.ts        # 集成测试
├── public/                       # 静态文件
│   └── index.html               # 前端界面
├── migrations/                   # 数据库迁移
├── scripts/                      # 脚本工具
├── static/                       # 静态资源
├── package.json                  # 项目配置
├── tsconfig.json                # TypeScript 配置
├── langgraph.json               # LangGraph 配置
├── jest.config.js               # Jest 测试配置
└── .cursorrules                 # 当前文件
```

## 编码规范

### 1. 文件命名和路径
- **服务器代码**: 放在 `src/server/` 目录
- **Agent 核心**: 放在 `src/agent/` 目录
- **配置文件**: 放在 `src/config/` 目录
- **示例代码**: 放在 `src/examples/` 目录
- **导入路径**: 使用相对路径，从当前文件位置开始

### 2. 导入路径示例
```typescript
// 从 src/server/app.ts 导入 agent
import { createText2SQLAgent } from '../agent/text2sqlPlanGraph.js';

// 从 src/agent/nodes/ 导入其他节点
import { plannerNode } from './plannerNode.js';
```

### 3. TypeScript 和模块
- 使用 ES 模块 (.js 扩展名在导入中)
- 严格的 TypeScript 类型检查
- 导出使用 named exports 优先于 default exports

### 4. 服务器相关
- 主服务器文件: `src/server/app.ts`
- 使用 Hono 框架 + SSE 流式响应
- API 路由前缀: `/api/`
- 健康检查: `/status`

## 开发命令

```bash
# 启动开发服务器
npm run start:dev

# 构建项目
npm run build

# 运行测试
npm run test
npm run test:int

# 格式化代码
npm run format

# 代码检查
npm run lint
```

## 环境变量要求

```env
# LLM API Keys
OPENAI_API_KEY=your_openai_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key

# ClickHouse (数据查询)
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_USERNAME=default
CLICKHOUSE_PASSWORD=
CLICKHOUSE_DATABASE=hubble

# 服务器配置
PORT=3000
NODE_ENV=development
```

## 重要注意事项

### 🚨 文件路径修改规则
1. **服务器代码** 必须放在 `src/server/` 目录
2. **主服务器文件** 是 `src/server/app.ts` (不是 src/server.ts)
3. **启动脚本** 使用 `src/server/app.ts`
4. **导入路径** 必须根据文件位置调整

### 🚨 Agent 架构
1. 基于 LangGraph 的 Plan-and-Execute 模式
2. 支持 RAG 增强的 SQL 生成
3. 错误恢复和重试机制
4. 流式响应支持

## 开发最佳实践

1. **遵循 KISS 原则** - 保持简单
2. **先查看项目结构** - 确认文件位置
3. **使用正确的导入路径** - 根据相对位置调整
4. **环境变量配置** - 确保必要的环境变量配置正确

## 故障排除

### 常见问题
1. **导入路径错误**: 检查文件的相对位置
2. **服务器启动失败**: 确认路径是 `src/server/app.ts`
3. **TypeScript 错误**: 确认 .js 扩展名在导入中

### 调试命令
```bash
# 检查项目结构
ls -la src/

# 测试服务器启动
npm run start

# 查看环境变量
echo $OPENAI_API_KEY
```

记住：每次修改文件位置时，都要相应更新导入路径和配置。这个规则文件会帮助避免路径错误！ 