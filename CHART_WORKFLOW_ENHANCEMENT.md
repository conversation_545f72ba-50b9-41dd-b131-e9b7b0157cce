# Chart Workflow Enhancement: 添加 Data Display Phase

## 概述
为 `createWorkflow` 增加了一个新的流程步骤 `data_display`，在生成 chart 前先生成数组结果，类似于 `text2sqlAgent.ts` 中 `data_display` phase 的功能。

## 修改内容

### 1. ChartAgent 修改 (`src/agent/chartAgent.ts`)

#### 新增 `processDataDisplay` 方法
- 添加了新的 `processDataDisplay` 方法作为 Step 2
- 处理数据显示逻辑，类似于 text2sqlAgent 的 data_display phase
- 返回处理后的数据状态

#### 更新路由逻辑 (`routeNext` 方法)
- 增加了对 `data_display` 步骤完成状态的检查
- 修改流程：`text2sql` → `data_display` → `chart_generation` → `complete`
- 确保在生成图表前先处理数据显示

#### 更新工作流定义 (`createWorkflow` 方法)
- 添加了 `data_display` 节点
- 更新了条件边缘路由，支持新的流程步骤
- 工作流现在包含四个节点：`text2sql`, `data_display`, `chart_generation`, `complete`

### 2. 服务器路由修改 (`src/server/chartRoutes.ts`)

#### 更新 Phase 映射
- 在 `handlePhaseTransition` 中添加了 `data_display` 节点到 phase 的映射
- 支持新的 `data_display` phase

#### 新增 Data Display Phase 处理
- 添加了 `data_display` phase 完成的处理逻辑
- 实现了数据限制功能（最大 1000 行，类似 text2sqlAgent）
- 在 phase 结束时返回处理后的数据数组，包含：
  - `data`: 限制后的数据数组
  - `rowCount`: 返回的行数
  - `originalRowCount`: 原始数据行数
  - 其他 phase 元数据

## 新的工作流程

```
START → text2sql → data_display → chart_generation → complete → END
```

### Phase 流程说明：
1. **plan**: 规划阶段
2. **sql_generation**: SQL 生成和执行阶段
3. **data_display**: 数据显示处理阶段（新增）
4. **chart_display**: 图表生成和显示阶段

## 功能特性

### Data Display Phase 特性：
- 处理 SQL 查询结果数据
- 限制返回数据行数（最大 1000 行）
- 提供原始行数和处理后行数信息
- 为图表生成准备数据
- 与 text2sqlAgent 的 data_display phase 保持一致的数据格式

### 流式响应支持：
- 支持 SSE 流式响应
- 每个 phase 都有明确的开始和结束事件
- `data_display` phase 结束时返回数组数据
- 保持与现有 API 的兼容性

## 测试

创建了测试文件 `test_chart_workflow.js` 来验证：
- 工作流创建是否成功
- 新的节点是否正确添加
- 流式执行是否按预期工作

## 使用示例

```javascript
import { createChartAgent } from './src/agent/chartAgent.js';

const chartAgent = createChartAgent();
const stream = await chartAgent.stream("Show me the top 10 tokens by market cap");

for await (const event of stream) {
  // 现在会包含 data_display phase 的事件
  // 在 chart_generation 之前会先处理数据显示
}
```

## 兼容性

- 保持与现有 API 的完全兼容性
- 不影响现有的图表生成功能
- 只是在流程中增加了一个数据处理步骤
- 客户端可以选择性地处理新的 `data_display` phase 事件
