// Simple test to verify the chart workflow with data_display phase
import { createChartAgent } from './src/agent/chartAgent.js';

async function testChartWorkflow() {
  console.log('🧪 Testing Chart Workflow with Data Display Phase');
  
  try {
    const chartAgent = createChartAgent();
    const workflow = chartAgent.createWorkflow();
    
    console.log('✅ Workflow created successfully');
    console.log('📋 Workflow nodes:', workflow.getGraph().nodes.map(n => n.id));
    
    // Test streaming to see the phases
    console.log('\n🔄 Testing streaming workflow...');
    const stream = await chartAgent.stream("Show me the top 10 tokens by market cap");
    
    let phaseCount = 0;
    for await (const event of stream) {
      const nodeNames = Object.keys(event);
      const nodeName = nodeNames[0];
      const stateData = Object.values(event)[0];
      
      console.log(`📍 Phase ${++phaseCount}: ${nodeName}`);
      if (stateData.steps && stateData.steps.length > 0) {
        const lastStep = stateData.steps[stateData.steps.length - 1];
        console.log(`   Step: ${lastStep.step} - ${lastStep.status} - ${lastStep.message}`);
      }
      
      // Stop after a few phases to avoid long execution
      if (phaseCount >= 3) {
        console.log('🛑 Stopping test after 3 phases');
        break;
      }
    }
    
    console.log('\n✅ Test completed successfully');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testChartWorkflow();
