import { StateGraph, Annotation, START, END } from "@langchain/langgraph";
import { RunnableConfig } from "@langchain/core/runnables";
import { LLMService } from "../services/llmService.js";
import { ChartGenerationService } from "../services/chartGenerationService.js";
import { createText2SQLAgent } from "./text2sqlAgent.js";
import { Text2SQLConfig, getConfig } from "../config/text2sql.js";
import { DatabaseSchema } from "../config/database.js";

// Chart Agent State
const ChartAgentState = Annotation.Root({
  /**
   * User's natural language query
   */
  userQuery: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "",
  }),

  /**
   * Database schema
   */
  schema: Annotation<DatabaseSchema>({
    reducer: (x, y) => y ?? x,
    default: () => ({ tables: [] }),
  }),

  /**
   * Generated SQL query
   */
  sqlQuery: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "",
  }),

  /**
   * SQL execution results
   */
  sqlResults: Annotation<any[]>({
    reducer: (x, y) => y ?? x ?? [],
    default: () => [],
  }),



  /**
   * Generated chart result
   */
  chartResult: Annotation<{
    chartType: string;
    htmlCode: string;
    generationTime: number;
  } | null>({
    reducer: (x, y) => y ?? x,
    default: () => null,
  }),

  /**
   * Processing steps for transparency
   */
  steps: Annotation<Array<{
    step: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    message: string;
    timestamp: string;
  }>>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),

  /**
   * Any errors during processing
   */
  errors: Annotation<string[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),

  /**
   * Whether the entire process is complete
   */
  isComplete: Annotation<boolean>({
    reducer: (x, y) => y ?? x ?? false,
    default: () => false,
  }),
});

type ChartAgentStateType = typeof ChartAgentState.State;

/**
 * Chart Generation Agent
 * Uses Text2SQL as a tool to generate charts from natural language queries
 */
export class ChartGenerationAgent {
  private config: Text2SQLConfig;
  private llmService: LLMService;
  private chartService: ChartGenerationService;
  private text2sqlAgent: any;

  constructor(config?: Partial<Text2SQLConfig>) {
    this.config = { ...getConfig(), ...config };
    this.llmService = new LLMService(this.config);
    this.chartService = new ChartGenerationService(this.llmService);
    this.text2sqlAgent = createText2SQLAgent(this.config);
  }

  /**
   * Step 1: Execute Text2SQL to get data
   */
  private async executeText2SQL(
    state: ChartAgentStateType,
    _config?: RunnableConfig
  ): Promise<Partial<ChartAgentStateType>> {
    const stepStart = Date.now();
    
    try {
      // Add step
      const newStep = {
        step: 'text2sql',
        status: 'running' as const,
        message: 'Executing Text2SQL query...',
        timestamp: new Date().toISOString(),
      };

      console.log(`🔍 [Chart Agent] Executing Text2SQL for: ${state.userQuery}`);

      // Execute Text2SQL - now returns array directly
      const sqlResults = await this.text2sqlAgent.execute(
        state.userQuery,
        state.schema,
        _config
      );

      // For chart generation, we don't need the SQL query text, just the data
      const sqlQuery = `-- SQL query generated for: ${state.userQuery}`;

      console.log(`✅ [Chart Agent] Text2SQL completed in ${Date.now() - stepStart}ms`);
      console.log(`📊 [Chart Agent] Got ${Array.isArray(sqlResults) ? sqlResults.length : 0} rows of data`);

      return {
        sqlQuery,
        sqlResults,
        steps: [{
          ...newStep,
          status: 'completed',
          message: `Text2SQL executed successfully. Generated SQL and retrieved ${sqlResults.length} rows.`,
        }],
      };

    } catch (error) {
      console.error(`❌ [Chart Agent] Text2SQL failed:`, error);
      return {
        errors: [`Text2SQL execution failed: ${error instanceof Error ? error.message : String(error)}`],
        steps: [{
          step: 'text2sql',
          status: 'failed' as const,
          message: `Text2SQL failed: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: new Date().toISOString(),
        }],
      };
    }
  }

  /**
   * Step 2: Generate chart from SQL results
   */
  private async generateChart(
    state: ChartAgentStateType,
    _config?: RunnableConfig
  ): Promise<Partial<ChartAgentStateType>> {
    const stepStart = Date.now();

    try {
      // Add step
      const newStep = {
        step: 'chart_generation',
        status: 'running' as const,
        message: 'Generating chart from data...',
        timestamp: new Date().toISOString(),
      };

      console.log(`🎨 [Chart Agent] Generating chart for ${state.sqlResults?.length || 0} rows`);

      // Generate chart (ChartGenerationService will handle empty data)
      const chartResult = await this.chartService.generateChart({
        data: state.sqlResults || [],
        userQuery: state.userQuery,
      }, _config);

      console.log(`✅ [Chart Agent] Chart generated in ${Date.now() - stepStart}ms`);
      console.log(`📈 [Chart Agent] Chart type: ${chartResult.chartType}`);

      return {
        chartResult,
        isComplete: true,
        steps: [{
          ...newStep,
          status: 'completed',
          message: `Chart generated successfully. Type: ${chartResult.chartType}`,
        }],
      };

    } catch (error) {
      console.error(`❌ [Chart Agent] Chart generation failed:`, error);
      return {
        errors: [`Chart generation failed: ${error instanceof Error ? error.message : String(error)}`],
        steps: [{
          step: 'chart_generation',
          status: 'failed' as const,
          message: `Chart generation failed: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: new Date().toISOString(),
        }],
      };
    }
  }

  /**
   * Router: Determines next step
   */
  private routeNext(state: ChartAgentStateType): string {
    // If there are errors, complete with error
    if (state.errors.length > 0) {
      return "complete";
    }

    // If already complete, go to end
    if (state.isComplete) {
      return "complete";
    }

    // Check if we have attempted Text2SQL
    const hasCompletedText2SQL = state.steps.some(step => 
      step.step === 'text2sql' && step.status === 'completed'
    );

    // If we haven't tried Text2SQL yet, execute it
    if (!hasCompletedText2SQL) {
      return "text2sql";
    }

    // If Text2SQL ran but returned no data, end the process
    if (hasCompletedText2SQL && (!state.sqlResults || state.sqlResults.length === 0)) {
      console.log("⏹️ [Chart Agent] No data returned from Text2SQL. Ending workflow.");
      return "complete";
    }

    // If we have data and haven't generated a chart, do it now
    if (hasCompletedText2SQL && !state.chartResult) {
      return "chart_generation";
    }

    // Everything done
    return "complete";
  }

  /**
   * Completion node
   */
  private async complete(
    state: ChartAgentStateType,
    _config?: RunnableConfig
  ): Promise<Partial<ChartAgentStateType>> {
    console.log(`🏁 [Chart Agent] Process complete`);
    
    return {
      isComplete: true,
      steps: [{
        step: 'completion',
        status: 'completed' as const,
        message: state.errors.length > 0 
          ? `Process completed with ${state.errors.length} errors`
          : 'Process completed successfully',
        timestamp: new Date().toISOString(),
      }],
    };
  }

  /**
   * Create the workflow graph
   */
  public createWorkflow() {
    const workflow = new StateGraph(ChartAgentState)
      .addNode("text2sql", this.executeText2SQL.bind(this))
      .addNode("chart_generation", this.generateChart.bind(this))
      .addNode("complete", this.complete.bind(this))
      
      .addEdge(START, "text2sql")
      .addEdge("complete", END)
      
      .addConditionalEdges("text2sql", this.routeNext.bind(this), {
        text2sql: "text2sql",
        chart_generation: "chart_generation",
        complete: "complete",
      })
      .addConditionalEdges("chart_generation", this.routeNext.bind(this), {
        complete: "complete",
      });

    return workflow.compile();
  }

  /**
   * Execute the chart generation workflow
   */
  public async execute(
    userQuery: string,
    schema?: DatabaseSchema,
    config?: RunnableConfig
  ) {
    if (!userQuery?.trim()) {
      throw new Error("User query cannot be empty");
    }

    const workflow = this.createWorkflow();
    const initialState: ChartAgentStateType = {
      userQuery,
      schema: schema || { tables: [] },
      sqlQuery: "",
      sqlResults: [],
      chartResult: null,
      steps: [],
      errors: [],
      isComplete: false,
    };

    console.log(`🚀 [Chart Agent] Starting workflow for: ${userQuery}`);

    try {
      const result = await workflow.invoke(initialState, {
        ...config,
        recursionLimit: this.config.execution.recursionLimit
      });
      
      console.log(`✅ [Chart Agent] Workflow completed`);
      console.log(`📊 [Chart Agent] Steps: ${result.steps.length}`);
      console.log(`❌ [Chart Agent] Errors: ${result.errors.length}`);
      
      // Return simplified result with just the essential data
      return {
        chartResult: result.chartResult,
        sqlQuery: result.sqlQuery,
        sqlResults: result.sqlResults,
        steps: result.steps,
        errors: result.errors,
        isComplete: result.isComplete,
      };
      
    } catch (error) {
      console.error(`❌ [Chart Agent] Workflow failed:`, error);
      throw error;
    }
  }

  /**
   * Stream the workflow execution
   */
  public async stream(
    userQuery: string,
    schema?: DatabaseSchema,
    config?: RunnableConfig
  ) {
    const workflow = this.createWorkflow();
    const initialState: ChartAgentStateType = {
      userQuery,
      schema: schema || { tables: [] },
      sqlQuery: "",
      sqlResults: [],
      chartResult: null,
      steps: [],
      errors: [],
      isComplete: false,
    };

    console.log(`🚀 [Chart Agent] Starting streaming workflow`);

    try {
      return workflow.stream(initialState, {
        ...config,
        recursionLimit: this.config.execution.recursionLimit
      });
    } catch (error) {
      console.error(`❌ [Chart Agent] Streaming failed:`, error);
      throw error;
    }
  }
}

// Export functions for easy usage
export const createChartAgent = (config?: Partial<Text2SQLConfig>) => {
  return new ChartGenerationAgent(config);
};

export const executeChartGeneration = async (
  userQuery: string,
  schema?: DatabaseSchema,
  config?: RunnableConfig
) => {
  const agent = new ChartGenerationAgent();
  return await agent.execute(userQuery, schema, config);
}; 