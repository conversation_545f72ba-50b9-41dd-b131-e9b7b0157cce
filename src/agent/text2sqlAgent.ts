import { StateGraph, END, START } from "@langchain/langgraph";
import { RunnableConfig } from "@langchain/core/runnables";
import {
  Text2SQLPlanState,
  DatabaseSchema,
  createInitialState,
} from "./state.js";
import { Text2SQLConfig, getConfig } from "../config/text2sql.js";
import { LLMService } from "../services/llmService.js";
import { RAGService } from "../services/ragService.js";
import { FormatService } from "../services/formatService.js";
import { plannerNode } from "./nodes/plannerNode.js";
import { executorNode } from "./nodes/executorNode.js";
import { ragNode } from "./nodes/ragNode.js";
import { recoveryNode } from "./nodes/recoveryNode.js";
import { completionNode } from "./nodes/completionNode.js";
import { analyzeClickHouseError } from "./utils.js";

/**
 * Text2SQL Plan-and-Execute Agent (Simplified)
 */
export class Text2SQLPlanAgent {
  private config: Text2SQLConfig;
  private llmService: LLMService;
  private ragService: RAGService;
  private formatService: FormatService;

  constructor(config?: Partial<Text2SQLConfig>) {
    this.config = { ...getConfig(), ...config };
    this.llmService = new LLMService(this.config);
    this.ragService = new RAGService(this.config.rag);
    this.formatService = new FormatService();
  }

  /**
   * Router function: Determines next step in the workflow
   */
  private routeNext(state: typeof Text2SQLPlanState.State): string {
    // Add debug logging
    console.log(
      `[Router Debug] currentStepIndex: ${state.currentStepIndex}, plan.length: ${state.plan.length}, completedSteps.length: ${state.completedSteps.length}, isComplete: ${state.isComplete}, processedResults: ${state.processedResults ? 'exists' : 'null'}`,
    );

    // Check if explicitly marked as complete
    if (state.isComplete) {
      console.log(`[Router] State already marked as complete, should not route further`);
      // This should not happen if the workflow is properly configured
      // But if it does, we'll route to completion one more time
      return "completion";
    }

    // Validate state first and handle validation errors gracefully
    const validationErrors = state.errors.filter(
      (error) =>
        error.includes("exceed plan length") ||
        error.includes("State validation failed"),
    );

    if (validationErrors.length > 0) {
      console.log(
        `[Router] State validation errors detected, forcing completion`,
      );
      return "completion";
    }

    const lastStep =
      state.completedSteps.length > 0
        ? state.completedSteps[state.completedSteps.length - 1]
        : undefined;
    const hasError =
      lastStep?.status === "failed" ||
      (state.errors.length > 0 && validationErrors.length === 0);

    // 1. Check for errors first (but not validation errors)
    if (hasError) {
      // Check retry limits before routing to recovery
      const errorCount = state.completedSteps.filter(
        (s) => s.status === "failed",
      ).length;
      
      // Use same retry logic as recovery node
      const lastError = lastStep?.error || "";
      const errorAnalysis = analyzeClickHouseError(lastError);
      const maxRetries = errorAnalysis.type === "resource_limit" ? 3 : 2;
      
      if (errorCount >= maxRetries) {
        console.log(`[Router] Max retries (${maxRetries}) exceeded, routing to completion`);
        return "completion";
      }
      
      console.log(`[Router] Error detected (${errorCount}/${maxRetries} retries), routing to recovery`);
      return "recovery";
    }

    // 2. Check if plan is complete (with safety checks)
    if (
      state.currentStepIndex >= state.plan.length ||
      state.completedSteps.length >= state.plan.length
    ) {
      console.log(`[Router] Plan complete, routing to completion`);
      return "completion";
    }

    // 3. Safety check for plan bounds
    if (
      state.currentStepIndex < 0 ||
      state.currentStepIndex >= state.plan.length
    ) {
      console.log(`[Router] Step index out of bounds, routing to completion`);
      return "completion";
    }

    // 4. Check if we need RAG for query steps
    const currentStep = state.plan[state.currentStepIndex];
    if (currentStep && currentStep.toLowerCase().includes("[query]")) {
      const ragHasRun = state.context?.ragExamples !== undefined;
      if (!ragHasRun) {
        console.log(`[Router] RAG needed for query step, routing to rag`);
        return "rag";
      }
    }

    // 5. Execute the next step
    console.log(`[Router] Executing next step, routing to executor`);
    return "executor";
  }

  /**
   * Create the Plan-and-Execute Text2SQL workflow
   */
  public createWorkflow() {
    const workflow = new StateGraph(Text2SQLPlanState)
      .addNode("planner", (state, config) =>
        plannerNode(state, config, this.llmService, this.config),
      )
      .addNode("rag", (state, config) =>
        ragNode(state, config, this.ragService),
      )
      .addNode("executor", (state, config) =>
        executorNode(
          state,
          config,
          this.llmService,
          this.ragService,
          this.config,
        ),
      )
      .addNode("recovery", (state, config) =>
        recoveryNode(state, config, this.llmService, this.config),
      )
      .addNode("completion", (state, config) =>
        completionNode(state, config, this.formatService, this.llmService),
      )
      // Remove chart generation nodes

      .addEdge(START, "planner")
      .addEdge("rag", "executor")
      .addEdge("completion", END)

      // Conditional routing
      .addConditionalEdges("planner", this.routeNext.bind(this), {
        executor: "executor",
        recovery: "recovery",
        completion: "completion",
        rag: "rag",
      })
      .addConditionalEdges("executor", this.routeNext.bind(this), {
        executor: "executor",
        recovery: "recovery",
        completion: "completion",
        rag: "rag",
      })
      .addConditionalEdges("recovery", this.routeNext.bind(this), {
        executor: "executor",
        recovery: "recovery",
        completion: "completion",
        rag: "rag",
      })
      // Remove chart generation routing

    return workflow.compile();
  }

  /**
   * Execute Text2SQL agent workflow
   */
  public async execute(
    userQuery: string,
    schema?: DatabaseSchema,
    config?: RunnableConfig,
  ) {
    if (!userQuery?.trim()) {
      throw new Error("User query cannot be empty");
    }

    const workflow = this.createWorkflow();
    const initialState = createInitialState(userQuery, schema);

    console.log(`🤖 Text2SQL Plan-and-Execute Agent`);
    console.log(`📝 User Query: ${userQuery}`);

    try {
      const finalResult = await workflow.invoke(initialState, {
        ...config,
        recursionLimit: this.config.execution.recursionLimit
      });
      // Return the raw data array instead of the full state
      return finalResult.processedResults || [];
    } catch (error) {
      console.error("❌ Error executing Text2SQL workflow:", error);
      throw error;
    }
  }

  // Remove chart execution method

  /**
   * Stream execution
   */
  public async stream(
    userQuery: string,
    schema?: DatabaseSchema,
    config?: RunnableConfig,
  ) {
    if (!userQuery?.trim()) {
      throw new Error("User query cannot be empty");
    }

    const workflow = this.createWorkflow();
    const initialState = createInitialState(userQuery, schema);

    console.log(`🤖 Text2SQL Plan-and-Execute Agent`);
    console.log(`📝 User Query: ${userQuery}`);

    try {
      return workflow.stream(initialState, {
        ...config,
        recursionLimit: this.config.execution.recursionLimit
      });
    } catch (error) {
      console.error("❌ Error streaming Text2SQL workflow:", error);
      throw error;
    }
  }

  // Remove chart streaming method

  public getStatus() {
    return {
      config: this.config,
      services: {
        llm: this.llmService.getConfig(),
        rag: this.ragService.getStatus(),
      },
    };
  }
}

// ========================================
// EXPORTS
// ========================================

export const createText2SQLPlanWorkflow = (
  config?: Partial<Text2SQLConfig>,
) => {
  const agent = new Text2SQLPlanAgent(config);
  return agent.createWorkflow();
};

export const executeText2SQLPlan = async (
  userQuery: string,
  schema?: DatabaseSchema,
  config?: RunnableConfig,
): Promise<any[]> => {
  const agent = new Text2SQLPlanAgent();
  return await agent.execute(userQuery, schema, config);
};

export const executeText2SQL = async (
  userQuery: string,
  config?: RunnableConfig,
): Promise<any[]> => {
  const agent = new Text2SQLPlanAgent();
  return await agent.execute(userQuery, undefined, config);
};

// Default export
export const text2sqlGraph = createText2SQLPlanWorkflow();
text2sqlGraph.name = "Text2SQL Plan-and-Execute Agent";

export const createText2SQLAgent = (
  config?: Partial<Text2SQLConfig>,
) => {
  return new Text2SQLPlanAgent(config);
};

// Remove chart-related exports
