export interface SQLExecutionResult {
  sql: string;
  data: unknown[];
  rowCount: number;
  ragEnhanced: boolean;
  executionTime?: number;
}

export interface AnalysisResult {
  result: string;
  dataProcessed: number;
  executionTime?: number;
}

export interface StepInput {
  description: string;
  userQuery: string;
  context?: Record<string, unknown>;
  ragContext?: string;
}

export interface QueryStepInput extends StepInput {
  sql?: string;
}

export interface AnalysisStepInput extends StepInput {
  latestData?: unknown[];
}

export interface PlanData {
  tasks: string[];
  parallel?: number[][];
  reasoning?: string;
}

export interface ParallelExecutionGroup {
  groupId: number;
  stepIndices: number[];
}

export interface ExecutionContext {
  ragExamples?: string;
  parallelGroups?: number[][];
  [key: string]: unknown;
}

export interface StepTypeInfo {
  type: "query" | "analysis";
  description: string;
}

export interface LLMResponse {
  content: string;
  metadata?: Record<string, unknown>;
}

export interface ResponseFormatting {
  timestamp: string;
  queryExecuted: boolean;
  resultsCount: number;
}
