import { BaseMessage, BaseMessageLike } from "@langchain/core/messages";
import { Annotation, messagesStateReducer } from "@langchain/langgraph";
import {
  SQLExecutionResult,
  AnalysisResult,
  QueryStepInput,
  AnalysisStepInput,
  ExecutionContext,
} from "./types.js";

/**
 * Plan-and-Execute Text2SQL State Definition
 *
 * This state tracks the execution of a plan-based text2sql workflow
 */

export interface ExecutionStep {
  id: string;
  description: string;
  type: "query" | "analysis";
  status: "pending" | "running" | "completed" | "failed";
  input?: QueryStepInput | AnalysisStepInput;
  output?: SQLExecutionResult | AnalysisResult;
  error?: string;
  executionTime?: number;
}

export interface DatabaseSchema {
  tables: Array<{
    name: string;
    type: "clickhouse" | "postgresql";
    columns: Array<{
      name: string;
      type: string;
      description?: string;
    }>;
    description?: string;
  }>;
}

// 移除复杂的图表相关接口

// This is the primary state for Plan-and-Execute Text2SQL agent
export const Text2SQLPlanState = Annotation.Root({
  /**
   * Original user query
   */
  userQuery: Annotation<string>({
    reducer: (x, y) => y ?? x ?? "",
    default: () => "",
  }),

  /**
   * Database schema information
   */
  schema: Annotation<DatabaseSchema>({
    reducer: (x, y) => y ?? x,
    default: () => ({ tables: [] }),
  }),

  /**
   * Execution plan - list of steps to execute
   */
  plan: Annotation<string[]>({
    reducer: (x, y) => y ?? x ?? [],
    default: () => [],
  }),

  /**
   * Completed steps with their results
   */
  completedSteps: Annotation<ExecutionStep[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),

  /**
   * Current step being executed
   */
  currentStepIndex: Annotation<number>({
    reducer: (x, y) => y ?? x ?? 0,
    default: () => 0,
  }),

  /**
   * Processed/analyzed results
   */
  processedResults: Annotation<any>({
    reducer: (x, y) => y ?? x,
    default: () => null,
  }),

  /**
   * Final response to user
   */
  finalResponse: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "",
  }),

  /**
   * Execution errors
   */
  errors: Annotation<string[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),

  /**
   * Whether the execution is complete
   */
  isComplete: Annotation<boolean>({
    reducer: (x, y) => y ?? x ?? false,
    default: () => false,
  }),

  /**
   * Execution context for passing data between steps
   */
  context: Annotation<ExecutionContext>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({}),
  }),

  /**
   * Messages for maintaining conversation context (optional)
   */
  messages: Annotation<BaseMessage[], BaseMessageLike[]>({
    reducer: messagesStateReducer,
    default: () => [],
  }),
});

/**
 * Validation functions for state integrity
 */
export const validateText2SQLState = (
  state: typeof Text2SQLPlanState.State,
): string[] => {
  const errors: string[] = [];

  if (!state.userQuery.trim()) {
    errors.push("User query is required");
  }

  if (state.currentStepIndex < 0) {
    errors.push("Current step index cannot be negative");
  }

  // More lenient validation - allow currentStepIndex to equal plan.length for completion
  if (state.currentStepIndex > state.plan.length && state.plan.length > 0) {
    errors.push("Current step index exceeds plan length");
  }

  // More lenient validation - only error if significantly exceeding plan length
  // This can happen during recovery operations and is normal
  if (
    state.completedSteps.length > state.plan.length + 2 &&
    state.plan.length > 0
  ) {
    console.warn(
      `[State Warning] Completed steps (${state.completedSteps.length}) exceed plan length (${state.plan.length}) - this may indicate recovery or retry operations`,
    );
    // Don't add this as an error anymore - it's a warning
  }

  return errors;
};

/**
 * Helper functions for state management
 */
export const createInitialState = (
  userQuery: string,
  schema?: DatabaseSchema,
): typeof Text2SQLPlanState.State => {
  return {
    userQuery,
    schema: schema || { tables: [] },
    plan: [],
    completedSteps: [],
    currentStepIndex: 0,
    processedResults: null,
    finalResponse: "",
    errors: [],
    isComplete: false,
    context: {},
    messages: [],
  };
};
