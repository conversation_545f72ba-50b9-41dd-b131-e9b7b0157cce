import { PlanData, StepTypeInfo } from "./types.js";
import { PlanningError, SQLGenerationError } from "./errors.js";

/**
 * Safely parse JSON with robust error handling and format cleaning
 */
export function safeParseJSON<T>(jsonString: string, fallback?: T): T {
  try {
    // Clean and normalize the input string
    let cleanedString = jsonString.trim();
    
    // Remove any leading/trailing explanatory text and find JSON
    // Try multiple patterns to extract JSON from various formats
    const jsonExtractionPatterns = [
      // Standard markdown code blocks
      /```json\s*\n?([\s\S]*?)\n?\s*```/,
      /```\s*\n?([\s\S]*?)\n?\s*```/,
      
      // Inline code blocks
      /`([\s\S]*?)`/,
      
      // JSON object pattern (most permissive)
      /(\{[\s\S]*\})/,
      
      // Array pattern
      /(\[[\s\S]*\])/,
    ];

    let extractedJson = cleanedString;
    
    // Try each extraction pattern
    for (const pattern of jsonExtractionPatterns) {
      const match = cleanedString.match(pattern);
      if (match && match[1]) {
        const candidate = match[1].trim();
        // Basic validation that this looks like JSON
        if ((candidate.startsWith('{') && candidate.endsWith('}')) || 
            (candidate.startsWith('[') && candidate.endsWith(']'))) {
          extractedJson = candidate;
          break;
        }
      }
    }

    // Additional cleanup
    extractedJson = extractedJson
      .replace(/^\s*(?:Here's the plan:|Plan:|Output:|Response:|JSON:)\s*/i, '') // Remove common prefixes
      .replace(/\s*(?:Let me know if you need any modifications\.?|Hope this helps\.?)\s*$/i, '') // Remove common suffixes
      .trim();

    // Try to parse the cleaned JSON
    const parsed = JSON.parse(extractedJson);
    
    // Validate it's not null or undefined
    if (parsed === null || parsed === undefined) {
      throw new Error("Parsed JSON is null or undefined");
    }
    
    return parsed as T;
  } catch (error) {
    // If we have a fallback, use it
    if (fallback !== undefined) {
      console.warn(`[JSON Parse] Failed to parse JSON, using fallback:`, error);
      return fallback;
    }
    
    // Enhanced error reporting
    const errorMsg = error instanceof Error ? error.message : "Unknown error";
    console.error(`[JSON Parse] Failed to parse JSON:`, {
      error: errorMsg,
      originalString: jsonString.substring(0, 500) + (jsonString.length > 500 ? '...' : ''),
    });
    
    throw new PlanningError(
      `Failed to parse JSON: ${errorMsg}. Please ensure your response is valid JSON format.`,
      { originalString: jsonString.substring(0, 200) },
    );
  }
}

/**
 * Validate and parse plan data from LLM response
 */
export function parsePlanData(planText: string): PlanData {
  try {
    const planData = safeParseJSON<PlanData>(planText);

    if (!planData.tasks || !Array.isArray(planData.tasks)) {
      throw new PlanningError("Invalid plan format: missing tasks array");
    }

    if (planData.tasks.length === 0) {
      throw new PlanningError("Plan contains no tasks");
    }

    return planData;
  } catch (error) {
    if (error instanceof PlanningError) {
      throw error;
    }

    // Fallback: try to parse old format
    return parseFallbackPlanFormat(planText);
  }
}

/**
 * Fallback parser for legacy plan formats
 */
export function parseFallbackPlanFormat(planText: string): PlanData {
  const fallbackSteps = planText
    .split("\n")
    .filter((line) => {
      const trimmed = line.trim();
      return (
        trimmed.match(/^\d+\.\s*\[(Query|Analysis)\]/) ||
        trimmed.match(/^\[(Query|Analysis)\]/) ||
        trimmed.match(/^\d+\./)
      );
    })
    .map((line) => {
      const trimmed = line.replace(/^\d+\.\s*/, "").trim();
      if (trimmed.startsWith("[Query]") || trimmed.startsWith("[Analysis]")) {
        return trimmed;
      } else {
        // Auto-detect type for backward compatibility
        if (
          trimmed.toLowerCase().includes("query") ||
          trimmed.toLowerCase().includes("get") ||
          trimmed.toLowerCase().includes("retrieve")
        ) {
          return `[Query] ${trimmed}`;
        } else {
          return `[Analysis] ${trimmed}`;
        }
      }
    });

  if (fallbackSteps.length === 0) {
    throw new PlanningError("Failed to parse plan in any format");
  }

  return {
    tasks: fallbackSteps,
    parallel: [Array.from({ length: fallbackSteps.length }, (_, i) => i)],
  };
}

/**
 * Parse step type and description with validation
 */
export function parseStepType(stepString: string): StepTypeInfo {
  if (!stepString || typeof stepString !== "string") {
    throw new PlanningError("Invalid step string provided");
  }

  const trimmed = stepString.trim();

  if (trimmed.startsWith("[Query]")) {
    return {
      type: "query",
      description: trimmed.replace(/^\[Query\]\s*/, "").trim(),
    };
  } else if (trimmed.startsWith("[Analysis]")) {
    return {
      type: "analysis",
      description: trimmed.replace(/^\[Analysis\]\s*/, "").trim(),
    };
  }

  // Fallback for backward compatibility with type detection
  const lowerCase = trimmed.toLowerCase();
  if (
    lowerCase.includes("query") ||
    lowerCase.includes("get") ||
    lowerCase.includes("retrieve")
  ) {
    return { type: "query", description: trimmed };
  } else {
    return { type: "analysis", description: trimmed };
  }
}

/**
 * Safely extract SQL from LLM response
 */
export function extractSQL(sqlResponse: string): string {
  if (!sqlResponse || typeof sqlResponse !== "string") {
    throw new SQLGenerationError("Empty or invalid SQL response");
  }

  const trimmed = sqlResponse.trim();

  // Try different SQL extraction patterns
  const patterns = [
    /```sql\n([\s\S]*?)\n```/,
    /```\n([\s\S]*?)\n```/,
    /```([\s\S]*?)```/,
  ];

  let extractedSQL = "";

  for (const pattern of patterns) {
    const match = trimmed.match(pattern);
    if (match && match[1].trim()) {
      extractedSQL = match[1].trim();
      break;
    }
  }

  // If no code blocks found, check if the response looks like SQL
  if (
    !extractedSQL &&
    trimmed.match(/\b(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP)\b/i)
  ) {
    extractedSQL = trimmed;
  }

  if (!extractedSQL) {
    throw new SQLGenerationError("Could not extract valid SQL from response", {
      response: trimmed,
    });
  }

  // Handle multiple statements - only return the first valid SELECT statement
  const statements = extractedSQL
    .split(";")
    .map((stmt) => stmt.trim())
    .filter((stmt) => stmt.length > 0);

  if (statements.length > 1) {
    console.log(
      `[SQL Extract] Found ${statements.length} statements, using first SELECT statement only`,
    );

    // Find the first SELECT statement
    const selectStatement = statements.find(
      (stmt) =>
        stmt.toLowerCase().trim().startsWith("select") ||
        stmt.toLowerCase().trim().startsWith("with"),
    );

    if (selectStatement) {
      return selectStatement;
    } else {
      // If no SELECT found, return first statement but log warning
      console.log(
        `[SQL Extract] Warning: No SELECT statement found, using first statement`,
      );
      return statements[0];
    }
  }

  return extractedSQL;
}

/**
 * Create a timeout promise wrapper
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  errorMessage = "Operation timed out",
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(errorMessage)), timeoutMs);
    }),
  ]);
}

/**
 * Validate execution step index
 */
export function validateStepIndex(index: number, planLength: number): void {
  if (index < 0 || index >= planLength) {
    throw new Error(
      `Invalid step index ${index} for plan of length ${planLength}`,
    );
  }
}

/**
 * Format execution time for display
 */
export function formatExecutionTime(timeMs: number): string {
  if (timeMs < 1000) {
    return `${timeMs}ms`;
  } else if (timeMs < 60000) {
    return `${(timeMs / 1000).toFixed(1)}s`;
  } else {
    return `${(timeMs / 60000).toFixed(1)}m`;
  }
}

/**
 * Validate SQL query for common syntax errors before execution
 */
export function validateSQL(sql: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  const sqlLower = sql.toLowerCase().trim();

  // Basic structure checks
  if (!sql.trim()) {
    errors.push("SQL query is empty");
    return { isValid: false, errors };
  }

  // Check for dangerous operations (basic safety)
  const dangerousKeywords = [
    "drop",
    "delete",
    "truncate",
    "alter",
    "create",
    "insert",
    "update",
  ];
  const foundDangerous = dangerousKeywords.find(
    (keyword) =>
      sqlLower.includes(keyword + " ") || sqlLower.startsWith(keyword),
  );
  if (foundDangerous) {
    errors.push(
      `Potentially dangerous operation detected: ${foundDangerous.toUpperCase()}`,
    );
  }

  // The alias conflict check has been removed as it was overly restrictive
  // and did not align with actual ClickHouse behavior, causing false positives.
  // The database itself is the ultimate arbiter of valid SQL syntax.

  // Check for multiple statements (not allowed in single execution)
  const statements = sql.split(";").filter((stmt) => stmt.trim().length > 0);
  if (statements.length > 1) {
    errors.push(
      `Multiple statements detected (${statements.length} statements). Only single statements are allowed.`,
    );
  }

  // Check for basic SQL structure
  if (!sqlLower.includes("select") && !sqlLower.includes("with")) {
    errors.push("SQL query must contain SELECT statement");
  }

  // Check for unmatched parentheses
  const openParens = (sql.match(/\(/g) || []).length;
  const closeParens = (sql.match(/\)/g) || []).length;
  if (openParens !== closeParens) {
    errors.push(
      `Unmatched parentheses: ${openParens} open, ${closeParens} close`,
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Analyze and classify ClickHouse errors for targeted recovery
 */
export function analyzeClickHouseError(errorMessage: string): {
  type:
    | "data_type"
    | "syntax"
    | "function"
    | "logic"
    | "resource_limit"
    | "unknown";
  subtype?: string;
  suggestion?: string;
  isRecoverable: boolean;
} {
  const lowerError = errorMessage.toLowerCase();

  // Resource limit errors (memory, time, etc.)
  if (lowerError.includes("memory limit") || lowerError.includes("exceeded")) {
    if (lowerError.includes("memory limit (for query) exceeded")) {
      return {
        type: "resource_limit",
        subtype: "memory_exceeded",
        suggestion:
          "The query is too complex or scans too much data at once. It should be broken down into smaller, simpler queries (e.g., by time range or by stages).",
        isRecoverable: true, // Recoverable by replanning, not by fixing SQL
      };
    }
  }

  // Data type errors
  if (
    lowerError.includes("illegal type") ||
    lowerError.includes("type mismatch")
  ) {
    if (
      lowerError.includes("array(tuple") &&
      lowerError.includes("jsonextractstring")
    ) {
      return {
        type: "data_type",
        subtype: "json_on_array_tuple",
        suggestion:
          "Use arrayMap() and tupleElement() instead of JSONExtractString() for Array(Tuple()) fields",
        isRecoverable: true,
      };
    }
    if (lowerError.includes("array") && lowerError.includes("string")) {
      return {
        type: "data_type",
        subtype: "array_string_mismatch",
        suggestion:
          "Use array functions like arrayElement(), arrayMap(), arrayFilter() for Array fields",
        isRecoverable: true,
      };
    }
    return {
      type: "data_type",
      subtype: "general_type_mismatch",
      suggestion:
        "Check field data types and use appropriate ClickHouse functions",
      isRecoverable: true,
    };
  }

  // Function usage errors
  if (
    lowerError.includes("function") &&
    (lowerError.includes("should be") || lowerError.includes("wrong number"))
  ) {
    return {
      type: "function",
      subtype: "wrong_function_usage",
      suggestion: "Check function signature and argument types",
      isRecoverable: true,
    };
  }

  // Syntax errors
  if (
    lowerError.includes("syntax error") ||
    lowerError.includes("expected") ||
    lowerError.includes("unexpected")
  ) {
    return {
      type: "syntax",
      subtype: "sql_syntax",
      suggestion: "Fix SQL syntax according to ClickHouse grammar",
      isRecoverable: true,
    };
  }

  // Logic errors (less recoverable)
  if (lowerError.includes("table") && lowerError.includes("not exist")) {
    return {
      type: "logic",
      subtype: "table_not_found",
      suggestion: "Check table names and schema",
      isRecoverable: false,
    };
  }

  return {
    type: "unknown",
    isRecoverable: true,
  };
}

/**
 * Generate specific recovery prompt based on error analysis
 */
export function generateRecoveryPrompt(
  errorAnalysis: ReturnType<typeof analyzeClickHouseError>,
  errorMessage: string,
  failedSQL: string,
  userQuery: string,
  attemptNumber: number,
): string {
  const baseContext = `
User Query: ${userQuery}
Failed SQL: ${failedSQL}
Error: ${errorMessage}
Attempt: ${attemptNumber}
`;

  const formatInstructions = `

CRITICAL: Your response MUST be in the following EXACT format - a numbered list of executable steps:

1. [Query] SELECT corrected_sql_here FROM table_name WHERE conditions;
2. [Analysis] Analyze and format the results from step 1.

RULES:
- Each line must start with a number and dot (1., 2., etc.)
- Each step must have [Query] or [Analysis] tag
- [Query] steps must contain complete, executable SQL
- Do NOT provide explanations or descriptions
- Do NOT use markdown formatting
- Do NOT include any text before or after the numbered list

EXAMPLE RESPONSE FORMAT:
1. [Query] SELECT user_id, COUNT(*) as activity_count FROM user_actions WHERE date >= '2024-01-01' GROUP BY user_id;
2. [Analysis] Format the results to show top 10 most active users.`;

  // Handle strategic replanning for resource limits
  if (errorAnalysis.type === "resource_limit") {
    return `${baseContext}

STRATEGIC REPLANNING REQUIRED:
The previous query failed due to a resource limit error (${errorAnalysis.subtype}). This is not a syntax error.
The query is too complex and needs to be broken down into smaller, sequential steps.

MEMORY OPTIMIZATION STRATEGIES (CRITICAL FOR RECOVERY):
1. Use VERY CONSERVATIVE LIMITS: LIMIT 100 for GROUP BY, LIMIT 50 for simple queries
2. Add STRICT time filtering: Use last 7 days maximum, prefer last 24 hours
3. Simplify GROUP BY operations: Group by fewer columns
4. Use sampling for large datasets: SAMPLE 0.01 (1% sample) for exploration
5. Avoid complex aggregations: Use simple COUNT(*) instead of complex calculations
6. Replace complex operations with basic SELECT statements

RECOVERY SOLUTION APPROACH:
1. Create MUCH SIMPLER queries with very small LIMIT clauses (≤50)
2. Use RECENT time ranges only (last 24 hours maximum)
3. Reduce GROUP BY complexity: group by single column when possible
4. Each [Query] step must be extremely lightweight with strict LIMIT/WHERE
5. Use [Analysis] steps to process small result sets

CONSERVATIVE RECOVERY PATTERNS:
1. [Query] SELECT * FROM table WHERE date = today() LIMIT 50;
2. [Analysis] Analyze the limited sample and provide insights based on available data.

FOR AGGREGATIONS (VERY CONSERVATIVE):
1. [Query] SELECT column, COUNT(*) FROM table WHERE date = today() GROUP BY column LIMIT 20;
2. [Analysis] Review the top results and summarize findings.

FOR DAILY ANALYSIS (SINGLE DAY ONLY):
1. [Query] SELECT trader_wallet_address, COUNT(*) as tx_count FROM table WHERE toDate(toDateTime(trade_timestamp)) = today() GROUP BY trader_wallet_address ORDER BY tx_count DESC LIMIT 20;
2. [Analysis] Show the most active wallets for today only.

${formatInstructions}`;
  }

  switch (errorAnalysis.subtype) {
    case "json_on_array_tuple":
      return `${baseContext}

CLICKHOUSE DATA TYPE ERROR DETECTED:
You tried to use JSONExtractString() on an Array(Tuple()) field.

SPECIFIC SOLUTION:
The field 'token_transfer_flows' is of type Array(Tuple(String, String, String, String, UInt8, Bool)).
Each tuple contains: (from, to, amount, token, type, is_valid)

CORRECT CLICKHOUSE SYNTAX:
- To get 'to' addresses: arrayMap(x -> tupleElement(x, 2), token_transfer_flows)
- To get 'from' addresses: arrayMap(x -> tupleElement(x, 1), token_transfer_flows)  
- To get amounts: arrayMap(x -> tupleElement(x, 3), token_transfer_flows)
- To filter by token: arrayFilter(x -> tupleElement(x, 4) = 'target_token', token_transfer_flows)

EXAMPLE CORRECTION:
Wrong: JSONExtractString(token_transfer_flows, 'to')
Right: arrayMap(x -> tupleElement(x, 2), token_transfer_flows) as to_addresses

${formatInstructions}`;

    case "array_string_mismatch":
      return `${baseContext}

CLICKHOUSE ARRAY TYPE ERROR DETECTED:
You're using string functions on an Array field.

SOLUTION APPROACH:
Use ClickHouse array functions:
- arrayElement(array, index) - get specific element
- arrayMap(lambda, array) - transform elements  
- arrayFilter(lambda, array) - filter elements
- arrayJoin(array) - expand array to rows

${formatInstructions}`;

    case "wrong_function_usage":
      return `${baseContext}

CLICKHOUSE FUNCTION ERROR DETECTED:
Wrong function arguments or signature.

SOLUTION APPROACH:
1. Check the function documentation
2. Verify argument types match expected types
3. Use type conversion functions if needed (toString, toInt64, etc.)

${formatInstructions}`;

    default:
      if (attemptNumber >= 3) {
        return `${baseContext}

MULTIPLE ATTEMPTS FAILED - SIMPLIFIED APPROACH:
This is attempt #${attemptNumber}. Previous approaches haven't worked.

STRATEGY:
1. Use only basic, proven ClickHouse functions
2. Avoid complex nested queries
3. Focus on the core requirement: "${userQuery}"
4. Create a simple, reliable solution

${formatInstructions}`;
      }

      return `${baseContext}

ERROR DETECTED: ${errorAnalysis.suggestion || "General SQL error"}

SOLUTION APPROACH:
Fix the specific error and create a working query plan.

${formatInstructions}`;
  }
}
