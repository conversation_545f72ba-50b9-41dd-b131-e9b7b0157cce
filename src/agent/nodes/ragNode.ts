import { RunnableConfig } from "@langchain/core/runnables";
import { Text2SQLPlanState } from "../state.js";
import { RAGService } from "../../services/ragService.js";

/**
 * RAG Node: Retrieve relevant examples for SQL generation
 * This node fetches similar examples from the vector database to help with SQL generation
 */
export async function ragNode(
  state: typeof Text2SQLPlanState.State,
  _config: RunnableConfig | undefined,
  ragService: RAGService,
): Promise<Partial<typeof Text2SQLPlanState.State>> {
  console.log(`[RAG Node] 🔍 Retrieving relevant examples for query: "${state.userQuery}"`);

  try {
    // Check if RAG service is available
    const isAvailable = await ragService.isAvailable();
    if (!isAvailable) {
      console.log(`[RAG Node] ⚠️ RAG service not available, returning empty context`);
      return {
        context: {
          ...state.context,
          ragExamples: "",
        },
      };
    }

    // Retrieve relevant examples
    const ragExamples = await ragService.retrieveExamples(state.userQuery);
    
    if (ragExamples && ragExamples.trim()) {
      console.log(`[RAG Node] ✅ Retrieved relevant examples`);
      return {
        context: {
          ...state.context,
          ragExamples,
        },
      };
    } else {
      console.log(`[RAG Node] ⚠️ No relevant examples found`);
      return {
        context: {
          ...state.context,
          ragExamples: "",
        },
      };
    }
  } catch (error) {
    console.error(`[RAG Node] ❌ Error retrieving examples:`, error);
    
    // Return empty context to allow workflow to continue
    return {
      context: {
        ...state.context,
        ragExamples: "",
      },
    };
  }
}
