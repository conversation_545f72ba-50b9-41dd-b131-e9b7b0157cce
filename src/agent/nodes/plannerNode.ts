import { RunnableConfig } from "@langchain/core/runnables";
import { Text2SQLPlanState, validateText2SQLState } from "../state.js";
import { PLAN_TEXT2SQL_PROMPT } from "../../prompts/planExecutePrompts.js";
import { PlanningError } from "../errors.js";
import { parsePlanData } from "../utils.js";
import { LLMService } from "../../services/llmService.js";
import { Text2SQLConfig } from "../../config/text2sql.js";

/**
 * Planning Node: Creates execution plan with proper error handling
 */
export async function plannerNode(
  state: typeof Text2SQLPlanState.State,
  config: RunnableConfig | undefined,
  llmService: LLMService,
  agentConfig: Text2SQLConfig,
): Promise<Partial<typeof Text2SQLPlanState.State>> {
  console.log(`[Planning Start] Query: ${state.userQuery}`);

  try {
    // Validate state before processing - but be more lenient
    const validationErrors = validateText2SQLState(state);

    // Only fail on critical validation errors, not warnings
    const criticalErrors = validationErrors.filter(
      (error) =>
        !error.includes("exceed plan length") &&
        !error.includes("Current step index exceeds") &&
        error.includes("required"),
    );

    if (criticalErrors.length > 0) {
      throw new PlanningError(
        `Critical state validation failed: ${criticalErrors.join(", ")}`,
      );
    }

    // Log non-critical validation issues as warnings
    if (validationErrors.length > 0) {
      console.warn(
        `[Planning Warning] Non-critical validation issues:`,
        validationErrors,
      );
    }

    const prompt = PLAN_TEXT2SQL_PROMPT.replace("{userQuery}", state.userQuery);
    const systemPrompt =
      "You are a professional database query planning expert.";

    const response = await llmService.invoke(systemPrompt, prompt, config);

    // Parse and validate the plan using robust parsing
    const planData = parsePlanData(response.content);

    // Use LLM-generated tasks directly, with fallback for backward compatibility
    const stepLines = planData.tasks.map((task: string, index: number) => {
      // If task already has type prefix, use it directly
      if (task.startsWith("[Query]") || task.startsWith("[Analysis]")) {
        return task;
      }

      // Fallback for tasks without type prefix (backward compatibility)
      const isLastTask = index === planData.tasks.length - 1;
      const taskLower = task.toLowerCase();

      if (
        isLastTask &&
        planData.tasks.length > 1 &&
        (taskLower.includes("combine") ||
          taskLower.includes("organize") ||
          taskLower.includes("analyze") ||
          taskLower.includes("format"))
      ) {
        return `[Analysis] ${task}`;
      } else {
        return `[Query] ${task}`;
      }
    });

    // Validate plan constraints
    if (stepLines.length > agentConfig.execution.maxStepsPerPlan) {
      throw new PlanningError(
        `Plan exceeds maximum allowed steps: ${stepLines.length} > ${agentConfig.execution.maxStepsPerPlan}`,
      );
    }

    const defaultParallelInfo = [
      Array.from({ length: planData.tasks.length }, (_, i) => i),
    ];
    const parallelInfo = planData.parallel || defaultParallelInfo;

    console.log(
      `[Planning Complete] Plan created with ${stepLines.length} tasks:`,
      stepLines,
    );
    console.log(`[Planning Complete] Parallel execution groups:`, parallelInfo);

    return {
      plan: stepLines,
      currentStepIndex: 0,
      context: {
        parallelGroups: parallelInfo,
      },
    };
  } catch (error) {
    const errorMsg =
      error instanceof Error ? error.message : "Unknown planning error";
    console.log(`[Planning Error] ${errorMsg}`);

    return {
      errors: [errorMsg],
      isComplete: true,
      finalResponse:
        "Sorry, I cannot create a query plan. Please try again with a different query.",
    };
  }
}
