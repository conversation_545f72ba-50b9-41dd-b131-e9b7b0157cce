import { RunnableConfig } from "@langchain/core/runnables";
import { Text2SQLPlanState, ExecutionStep } from "../state.js";
import { SQLGenerationError } from "../errors.js";
import { extractSQL, validateSQL, withTimeout } from "../utils.js";
import { SQLExecutionResult, QueryStepInput } from "../types.js";
import { generateTEXT2SQLPrompt } from "../../prompts/text2sqlPrompt.js";
import { getDataBySql } from "../../utils/index.js";
import { LLMService } from "../../services/llmService.js";
import { Text2SQLConfig } from "../../config/text2sql.js";

/**
 * Execute SQL generation and execution step with RAG enhancement
 */
export async function executeSQLStep(
  stepDescription: string,
  state: typeof Text2SQLPlanState.State,
  config: RunnableConfig | undefined,
  llmService: LLMService,
  agentConfig: Text2SQLConfig,
): Promise<ExecutionStep> {
  // Get RAG examples from context (set by RAG node)
  const ragExamples = state.context?.ragExamples || "";

  // Build enhanced SQL generation prompt
  let enhancedPrompt = generateTEXT2SQLPrompt();

  // Add RAG examples if available
  if (ragExamples.trim()) {
    enhancedPrompt += `\n\n# RELEVANT SQL EXAMPLES (Retrieved from Knowledge Base)\n${ragExamples}\n`;
    enhancedPrompt += `\nIMPORTANT: Use the above examples as reference patterns, but adapt them to the specific requirements.`;
  }

  const systemPrompt = enhancedPrompt;
  const userPrompt = `Generate SQL query for: ${stepDescription}

CRITICAL Requirements:
1. Generate exactly ONE executable SQL statement (no multiple statements with semicolons)
2. Use UNION ALL to combine multiple tables in a single query if needed
3. Return SQL statement directly without code blocks, explanations, or semicolons
4. Follow correct database syntax rules (ClickHouse for hubble.* tables, PostgreSQL for public.* tables)
5. For the user query: "${state.userQuery}"`;

  const sqlResponse = await llmService.invoke(systemPrompt, userPrompt, config);
  const cleanSQL = extractSQL(sqlResponse.content);

  // Validate SQL before execution to catch common errors early
  const validation = validateSQL(cleanSQL);
  if (!validation.isValid) {
    throw new SQLGenerationError(
      `SQL validation failed: ${validation.errors.join("; ")}`,
      { sql: cleanSQL, validationErrors: validation.errors },
    );
  }

  // Execute SQL with proper timeout and error handling
  let queryResult: unknown;
  try {
    console.log(
      `[SQL Execution] Executing validated SQL: ${cleanSQL.substring(0, 100)}...`,
    );
    queryResult = await withTimeout(
      getDataBySql(cleanSQL),
      Math.min(agentConfig.execution.timeoutMs, 300000), // Match ClickHouse client timeout (5 minutes)
      "SQL execution timed out",
    );
  } catch (error) {
    const sqlError = new SQLGenerationError(
      `SQL execution failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      { sql: cleanSQL },
    );
    // Store SQL in the error for recovery purposes
    (sqlError as any).context = { sql: cleanSQL };
    throw sqlError;
  }

  const sqlResult: SQLExecutionResult = {
    sql: cleanSQL,
    data: Array.isArray(queryResult) ? queryResult : [queryResult],
    rowCount: Array.isArray(queryResult) ? queryResult.length : 1,
    ragEnhanced: !!ragExamples,
  };

  return {
    id: `step_${state.currentStepIndex + 1}`,
    description: stepDescription,
    type: "query",
    status: "completed",
    input: {
      description: stepDescription,
      userQuery: state.userQuery,
      ragContext: ragExamples ? "RAG retrieval used" : "RAG retrieval not used",
    } as QueryStepInput,
    output: sqlResult,
  };
}
