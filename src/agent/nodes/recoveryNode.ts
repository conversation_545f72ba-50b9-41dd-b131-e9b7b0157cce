import { RunnableConfig } from "@langchain/core/runnables";
import { Text2SQLPlanState } from "../state.js";
import { PlanningError } from "../errors.js";
import { analyzeClickHouseError, generateRecoveryPrompt } from "../utils.js";
import { LLMService } from "../../services/llmService.js";
import { Text2SQLConfig } from "../../config/text2sql.js";

/**
 * Recovery Node: Handles plan failure and attempts intelligent recovery
 */
export async function recoveryNode(
  state: typeof Text2SQLPlanState.State,
  config: RunnableConfig | undefined,
  llmService: LLMService,
  _agentConfig: Text2SQLConfig,
): Promise<Partial<typeof Text2SQLPlanState.State>> {
  console.log(`[Recovery] ⚠️ Error detected, initiating recovery process...`);

  const lastStep = state.completedSteps[state.completedSteps.length - 1];
  const errorCount = state.completedSteps.filter(
    (s) => s.status === "failed",
  ).length;
  const errorMessage = lastStep?.error || "";

  // Analyze the error to determine recovery strategy
  const errorAnalysis = analyzeClickHouseError(errorMessage);
  console.log(`[Recovery] Error analysis:`, errorAnalysis);

  // Check retry limits
  const maxRetries = errorAnalysis.type === "resource_limit" ? 3 : 2;

  if (errorCount >= maxRetries || !errorAnalysis.isRecoverable) {
    console.log(`[Recovery] Max retries exceeded or error not recoverable`);
    return {
      isComplete: true,
      finalResponse: `Unable to execute query after ${errorCount} attempts. Error type: ${errorAnalysis.type}. Last error: ${errorMessage}`,
    };
  }

  try {
    console.log(
      `[Recovery] Proceeding with recovery (attempt ${errorCount + 1}/${maxRetries})...`,
    );

    // Get the failed SQL for context
    const failedSQL =
      (lastStep?.input as any)?.sql ||
      (lastStep?.input as any)?.failedSQL ||
      (lastStep?.output as any)?.sql ||
      state.context?.lastExecutedSQL ||
      "SELECT 1";

    // Generate recovery prompt
    const recoveryPrompt = generateRecoveryPrompt(
      errorAnalysis,
      errorMessage,
      failedSQL,
      state.userQuery,
      errorCount + 1,
    );

    const systemPrompt =
      "You are a ClickHouse SQL expert specializing in error recovery. Generate corrected SQL that addresses the specific error type.";
    const recoveryResponse = await llmService.invoke(
      systemPrompt,
      recoveryPrompt,
      config,
    );

    const recoveryText = recoveryResponse.content;

    // Enhanced parsing: Extract numbered list format
    const lines = recoveryText
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);
    let newSteps: string[] = [];

    console.log(`[Recovery] Parsing LLM response:`, recoveryText.substring(0, 200) + "...");

    // Primary pattern: Look for numbered steps with [Query] or [Analysis] tags
    for (const line of lines) {
      if (line.match(/^\d+\.\s*\[(Query|Analysis)\]/)) {
        const step = line.replace(/^\d+\.\s*/, "");
        newSteps.push(step);
        console.log(`[Recovery] Found formatted step: ${step}`);
      }
    }

    // Fallback 1: Look for [Query] or [Analysis] tags without numbers
    if (newSteps.length === 0) {
      for (const line of lines) {
        if (line.match(/^\[(Query|Analysis)\]/)) {
        newSteps.push(line);
          console.log(`[Recovery] Found unumbered step: ${line}`);
    }
      }
    }

    // Fallback 2: Try to extract SQL from code blocks
    if (newSteps.length === 0) {
      const sqlPatterns = [
        /```sql\n([\s\S]*?)\n```/g,
        /```\n([\s\S]*?)\n```/g,
        /SELECT[\s\S]*?(?=;|$)/gi
      ];

      for (const pattern of sqlPatterns) {
        const matches = recoveryText.matchAll(pattern);
        for (const match of matches) {
          const sql = match[1] || match[0];
          if (sql && sql.trim().toUpperCase().startsWith('SELECT')) {
            newSteps.push(`[Query] ${sql.trim()}`);
            console.log(`[Recovery] Extracted SQL from code block: ${sql.trim().substring(0, 50)}...`);
            break;
          }
        }
        if (newSteps.length > 0) break;
      }
    }

    // Fallback 3: If still no steps found but there's content, try to intelligently wrap it
    if (newSteps.length === 0 && recoveryText.trim()) {
      const cleanText = recoveryText.trim();
      
      // Check if it looks like SQL
      if (cleanText.toUpperCase().includes('SELECT') && 
          cleanText.toUpperCase().includes('FROM')) {
        newSteps = [`[Query] ${cleanText}`];
        console.log(`[Recovery] Wrapped detected SQL as query step`);
      } else {
        // Last resort: Create a simple retry with basic approach
        console.log(`[Recovery] Could not parse response, creating simple retry plan`);

        if (errorAnalysis.type === "resource_limit") {
          // For memory errors, create an extremely simple query
          console.log(`[Recovery] Creating ultra-conservative query for memory error`);

          // Extract table name from failed SQL
          const tableMatch = failedSQL.match(/FROM\s+([\w.]+)/i);
          const tableName = tableMatch ? tableMatch[1] : 'hubble.distributed_dex_token_trade_transaction';

          // Create the simplest possible query
          const ultraSimpleQuery = `SELECT trader_wallet_address, COUNT(*) as tx_count FROM ${tableName} WHERE toDate(toDateTime(trade_timestamp)) = today() GROUP BY trader_wallet_address ORDER BY tx_count DESC LIMIT 20`;

          newSteps = [
            `[Query] ${ultraSimpleQuery}`,
            `[Analysis] Show the top active wallets for today only based on the limited sample.`
          ];
        } else {
          newSteps = [
            `[Query] SELECT * FROM (${failedSQL}) LIMIT 100`,
            `[Analysis] Review and format the limited results`
          ];
        }
      }
    }

    if (newSteps.length > 0) {
      console.log(
        `[Recovery Complete] New plan created with ${newSteps.length} steps`,
      );

      const newPlan =
        errorAnalysis.type === "resource_limit"
          ? newSteps
          : [...state.plan.slice(0, state.currentStepIndex - 1), ...newSteps];

      const newStepIndex =
        errorAnalysis.type === "resource_limit"
          ? 0
          : state.currentStepIndex - 1;

      return {
        plan: newPlan,
        errors: [],
        currentStepIndex: newStepIndex,
        completedSteps:
          errorAnalysis.type === "resource_limit" ? [] : state.completedSteps,
      };
    } else {
      throw new PlanningError(`Recovery failed to generate a valid new plan`);
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown recovery error";
    console.log(`[Recovery Error] ${errorMessage}`);
    return {
      isComplete: true,
      finalResponse: `I apologize, but I encountered an error and was unable to recover: ${errorMessage}`,
    };
  }
}
