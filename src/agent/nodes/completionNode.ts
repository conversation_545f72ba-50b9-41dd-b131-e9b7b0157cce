import { RunnableConfig } from "@langchain/core/runnables";
import { Text2SQLPlanState } from "../state.js";
import { SQLExecutionResult } from "../types.js";
import { FormatService } from "../../services/formatService.js";
import { LLMService } from "../../services/llmService.js";

/**
 * Completion Node: Returns raw query results without AI analysis
 */
export async function completionNode(
  state: typeof Text2SQLPlanState.State,
  _config: RunnableConfig | undefined,
  _formatService: FormatService,
  _llmService: LLMService,
): Promise<Partial<typeof Text2SQLPlanState.State>> {
  console.log(
    `[Completion] ✅ Processing completion - isComplete: ${state.isComplete}, processedResults: ${state.processedResults ? 'exists' : 'null'}`,
  );

  // Safety check - if already complete, don't process again
  if (state.isComplete) {
    console.log(
      `[Completion] State already marked as complete, avoiding reprocessing`,
    );
    // Return the current state without any changes to prevent loops
    return {};
  }

  // Safety check - ensure we have completed steps
  if (state.completedSteps.length === 0) {
    console.log(
      `[Completion] No completed steps found, returning empty array`,
    );
    return {
      isComplete: true,
      processedResults: [],
      finalResponse: "No results found",
    };
  }

  // Extract query results from all completed query steps
  const querySteps = state.completedSteps.filter(
    (step) => step.type === "query" && step.status === "completed" && step.output
  );

  const failedSteps = state.completedSteps.filter(
    (step) => step.status === "failed"
  );

  if (querySteps.length === 0) {
    console.log(`[Completion] No successful query steps found`);
    
    // Provide helpful error message based on failure patterns
    let errorMessage = "No query results found";
    if (failedSteps.length > 0) {
      const lastFailedStep = failedSteps[failedSteps.length - 1];
      const errorCount = failedSteps.length;
      
      if (errorCount >= 3) {
        errorMessage = `Query execution failed after ${errorCount} attempts. The last error was: ${lastFailedStep.error || 'Unknown error'}. This may be due to complex query requirements, data access restrictions, or schema limitations.`;
      } else {
        errorMessage = `Query execution failed (${errorCount} attempts). Error: ${lastFailedStep.error || 'Unknown error'}`;
      }
    }
    
    return {
      isComplete: true,
      processedResults: [],
      finalResponse: errorMessage,
    };
  }

  // If only one query step, return its data directly
  if (querySteps.length === 1) {
    const queryOutput = querySteps[0].output as SQLExecutionResult;
    console.log(`[Completion] Returning single query result with ${queryOutput.rowCount} rows`);
    return {
      isComplete: true,
      processedResults: queryOutput.data,
      finalResponse: `Query executed successfully. ${queryOutput.rowCount} rows returned.`,
    };
  }

  // For multiple query steps, combine all results
  const combinedResults: any[] = [];
  for (const step of querySteps) {
    const queryOutput = step.output as SQLExecutionResult;
    if (Array.isArray(queryOutput.data)) {
      combinedResults.push(...queryOutput.data);
    }
  }

  console.log(`[Completion] Returning combined results from ${querySteps.length} queries with ${combinedResults.length} total rows`);
  
  return {
    isComplete: true,
    processedResults: combinedResults,
    finalResponse: `All queries executed successfully. ${combinedResults.length} total rows returned.`,
  };
}
