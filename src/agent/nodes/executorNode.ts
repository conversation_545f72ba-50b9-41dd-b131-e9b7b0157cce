import { RunnableConfig } from "@langchain/core/runnables";
import { Text2SQLPlanState, ExecutionStep } from "../state.js";
import { ExecutionError, SQLGenerationError } from "../errors.js";
import {
  parseStepType,
  validateStepIndex,
  formatExecutionTime,
} from "../utils.js";
import { QueryStepInput } from "../types.js";
import { executeSQLStep } from "./sqlStep.js";
import { executeAnalysisStep } from "./analysisStep.js";
import { LLMService } from "../../services/llmService.js";
import { RAGService } from "../../services/ragService.js";
import { Text2SQLConfig } from "../../config/text2sql.js";

/**
 * Executor Node: Executes current step with comprehensive error handling
 */
export async function executorNode(
  state: typeof Text2SQLPlanState.State,
  config: RunnableConfig | undefined,
  llmService: LLMService,
  _ragService: RAGService,
  agentConfig: Text2SQLConfig,
): Promise<Partial<typeof Text2SQLPlanState.State>> {
  // Check if we're already complete or beyond the plan
  if (state.isComplete || state.currentStepIndex >= state.plan.length) {
    console.log(
      `[Executor] Execution already complete or beyond plan bounds, skipping`,
    );
    return {
      isComplete: true,
      finalResponse:
        state.finalResponse || "Execution completed but no final result.",
    };
  }

  try {
    validateStepIndex(state.currentStepIndex, state.plan.length);
  } catch (error) {
    console.log(`[Executor] Step validation failed, completing execution`);
    return {
      isComplete: true,
      finalResponse:
        state.finalResponse || "Execution completed but no final result.",
    };
  }

  const currentStep = state.plan[state.currentStepIndex];
  const stepId = `step_${state.currentStepIndex + 1}`;
  const startTime = Date.now();

  // Parse step type and description with validation
  const { type, description } = parseStepType(currentStep);

  console.log(
    `[Step Start] ${state.currentStepIndex + 1}/${state.plan.length}: [${type.toUpperCase()}] ${description}`,
  );

  try {
    let stepResult: ExecutionStep;

    // Execute based on parsed step type
    switch (type) {
      case "query":
        stepResult = await executeSQLStep(
          description,
          state,
          config,
          llmService,
          agentConfig,
        );
        break;
      case "analysis":
        stepResult = await executeAnalysisStep(
          description,
          state,
          config,
          llmService,
        );
        break;
      default:
        throw new ExecutionError(`Unknown step type: ${type}`);
    }

    stepResult.executionTime = Date.now() - startTime;

    console.log(
      `[Step Complete] ${state.currentStepIndex + 1}: ${stepResult.description} (${formatExecutionTime(stepResult.executionTime)})`,
    );

    return {
      completedSteps: [stepResult],
      currentStepIndex: state.currentStepIndex + 1,
      context: {
        ...state.context,
        [`step_${state.currentStepIndex + 1}_result`]: stepResult.output,
      },
    };
  } catch (error) {
    const errorMsg =
      error instanceof Error ? error.message : "Unknown execution error";
    console.log(`[Step Error] ${state.currentStepIndex + 1}: ${errorMsg}`);

    const failedStep: ExecutionStep = {
      id: stepId,
      description: currentStep,
      type: type,
      status: "failed",
      error: errorMsg,
      executionTime: Date.now() - startTime,
      input: {
        description: currentStep,
        userQuery: state.userQuery,
        failedSQL:
          error instanceof SQLGenerationError
            ? (error as any).context?.sql
            : undefined,
      } as QueryStepInput,
    };

    return {
      completedSteps: [failedStep],
      errors: [errorMsg],
      currentStepIndex: state.currentStepIndex + 1,
    };
  }
}
