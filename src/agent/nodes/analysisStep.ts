import { RunnableConfig } from "@langchain/core/runnables";
import { Text2SQLPlanState, ExecutionStep } from "../state.js";
import { DATA_ANALYSIS_PROMPT } from "../../prompts/planExecutePrompts.js";
import {
  SQLExecutionResult,
  AnalysisResult,
  AnalysisStepInput,
} from "../types.js";
import { LLMService } from "../../services/llmService.js";

/**
 * Execute analysis processing step with type safety
 */
export async function executeAnalysisStep(
  stepDescription: string,
  state: typeof Text2SQLPlanState.State,
  config: RunnableConfig | undefined,
  llmService: LLMService,
): Promise<ExecutionStep> {
  // Get the latest query data from completed steps
  const latestSQLStep = state.completedSteps
    .slice()
    .reverse()
    .find((step) => step.type === "query" && step.output);

  const latestData = latestSQLStep?.output
    ? (latestSQLStep.output as SQLExecutionResult).data
    : [];

  const prompt = DATA_ANALYSIS_PROMPT.replace(
    "{taskDescription}",
    stepDescription,
  )
    .replace("{userQuery}", state.userQuery)
    .replace(
      "{queryData}",
      JSON.stringify(
        Array.isArray(latestData) ? latestData.slice(0, 10) : latestData,
        null,
        2,
      ),
    )
    .replace("{context}", JSON.stringify(state.context, null, 2));

  const systemMessage =
    "You are a data analysis expert specializing in blockchain/DeFi data analysis. Provide complete analysis without privacy disclaimers.";

  const response = await llmService.invoke(systemMessage, prompt, config);

  const analysisResult: AnalysisResult = {
    result: response.content,
    dataProcessed: Array.isArray(latestData) ? latestData.length : 0,
  };

  return {
    id: `step_${state.currentStepIndex + 1}`,
    description: stepDescription,
    type: "analysis",
    status: "completed",
    input: {
      description: stepDescription,
      userQuery: state.userQuery,
      context: state.context,
      latestData: Array.isArray(latestData) ? latestData : [],
    } as AnalysisStepInput,
    output: analysisResult,
  };
}
