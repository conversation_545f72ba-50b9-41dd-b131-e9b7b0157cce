export class Text2SQLError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly context?: Record<string, unknown>,
  ) {
    super(message);
    this.name = "Text2SQLError";
  }
}

export class PlanningError extends Text2SQLError {
  constructor(message: string, context?: Record<string, unknown>) {
    super(message, "PLANNING_ERROR", context);
    this.name = "PlanningError";
  }
}

export class ExecutionError extends Text2SQLError {
  constructor(message: string, context?: Record<string, unknown>) {
    super(message, "EXECUTION_ERROR", context);
    this.name = "ExecutionError";
  }
}

export class SQLGenerationError extends Text2SQLError {
  constructor(message: string, context?: Record<string, unknown>) {
    super(message, "SQL_GENERATION_ERROR", context);
    this.name = "SQLGenerationError";
  }
}

export class RAGError extends Text2SQLError {
  constructor(message: string, context?: Record<string, unknown>) {
    super(message, "RAG_ERROR", context);
    this.name = "RAGError";
  }
}

export class TimeoutError extends Text2SQLError {
  constructor(message: string, context?: Record<string, unknown>) {
    super(message, "TIMEOUT_ERROR", context);
    this.name = "TimeoutError";
  }
}
