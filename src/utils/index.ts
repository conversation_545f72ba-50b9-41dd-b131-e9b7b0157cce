import "dotenv/config";
import { createClient } from "@clickhouse/client";
import { Client as PostgresClient } from "pg";

const clickhouseClient = createClient({
  url: process.env.CK_BASE_URL ?? "http://127.0.0.1:8123",
  username: "hubble_readonly",
  password: "egV7U!#tm2_QgB=@>.kW",
  database: "hubble",
  request_timeout: 300000, // 5 minutes timeout
});

// PostgreSQL client configuration
const createPostgresClient = () => {
  return new PostgresClient({
    host: process.env.PG_HOSTNAME ?? "localhost",
    port: parseInt(process.env.PG_PORT ?? "5432"),
    user: "readonly_user",
    database: process.env.PG_SOL_DB ?? "hubble", // Use hubble database for PostgreSQL
    password: "P8@dK6!mN2#f",
  });
};

// Function to determine database type based on SQL query
const getDatabaseType = (sql: string): "clickhouse" | "postgresql" => {
  // Check if SQL contains public.* tables (PostgreSQL)
  if (sql.includes("public.")) {
    return "postgresql";
  }

  // Check if SQL contains hubble.* tables (ClickHouse)
  if (sql.includes("hubble.")) {
    return "clickhouse";
  }

  // Default to ClickHouse for backward compatibility
  return "clickhouse";
};

export const getDataBySql = async (sql: string) => {
  const dbType = getDatabaseType(sql);

  try {
    if (dbType === "postgresql") {
      // PostgreSQL execution
      const pgClient = createPostgresClient();

      await pgClient.connect();

      const result = await pgClient.query(sql);
      await pgClient.end();

      console.log("Query Results:", result.rows);
      return result.rows;
    } else {
      const result = await clickhouseClient.query({
        query: sql,
        format: "JSONEachRow",
        query_params: {
          max_execution_time: 300, // 5 minutes in seconds
        },
      });
      const rows = await result.json();

      console.log("Query Results:", rows);
      return rows;
    }
  } catch (error) {
    console.error(`[Database Error] Failed to execute SQL query:`, error);
    throw error;
  }
};
