/**
 * Shared blockchain terminology definitions for Text2SQL system
 * This file contains standardized definitions to ensure consistency across all agents
 */

import {
  TABLE_MAPPING,
  formatDatabaseInfo,
} from "../config/schemaFormatter.js";

export const BLOCKCHAIN_BUSINESS_CONTEXT = `## Business Context: Solana Blockchain Data Analysis
This system analyzes Solana blockchain trading and transaction data including:
- **DEX trading records** (buy/sell transactions with price exchanges)
- **Token transfer records** (address-to-address token movements)
- **Token holdings and balance calculations**
- **Trading volume and price analytics**
- **OHLCV candlestick data for technical analysis**`;

export const BLOCKCHAIN_TERMINOLOGY = `## Critical Blockchain Terminology Understanding

### Trading Operations (交易)
- **买入/卖出/交易** = Market transactions on DEX/PumpFun with price exchanges
- **Keywords**: buy, sell, trade, trading, purchase, 买入, 卖出, 交易, 购买
- **Data Source**: ${TABLE_MAPPING.DEX_TRADING}, ${TABLE_MAPPING.PUMPFUN_TRADING}
- **Data Fields**: buy_amount, sell_amount, buy_price_sol, sell_price_sol, buy_sol_amount, sell_sol_amount
- **Characteristics**: Involves prices, fees, slippage, market activity

### Transfer Operations (转账)  
- **转入/转出/转账** = Direct token movements between addresses without price exchange
- **Keywords**: transfer, send, receive, 转入, 转出, 转账, 发送, 接收, 流向, 转移
- **Data Source**: ${TABLE_MAPPING.DEX_TRANSFERS}
- **Data Fields**: token_transfer_flows, from_address, to_address, transfer amounts
- **Characteristics**: No price exchange, pure token movement

### Holdings Analysis (持仓)
- **余额/持仓** = Current token balance and holder statistics
- **Keywords**: balance, holdings, owns, holders, 余额, 持仓, 拥有, 持有者
- **Data Source**: ${TABLE_MAPPING.TOKEN_HOLDERS} (PostgreSQL), calculated from trading + transfer data
- **Data Fields**: holders_num, holding_ratio, total_bought, total_sold
- **Calculation**: (Buy + Transfer In) - (Sell + Transfer Out)`;

export const DATABASE_ARCHITECTURE = formatDatabaseInfo();

export const DATA_SOURCE_MAPPING = `## Data Source Mapping
| User Intent | Data Type | Primary Tables | Database |
|-------------|-----------|----------------|----------|
| Trading queries | DEX/PumpFun trades | ${TABLE_MAPPING.DEX_TRADING} | ClickHouse |
| Transfer queries | Token movements | ${TABLE_MAPPING.DEX_TRANSFERS} | ClickHouse |
| Holdings queries | Balance analysis | ${TABLE_MAPPING.TOKEN_HOLDERS} | PostgreSQL |
| Price/OHLCV queries | Market data | hubble.*_ohlcv_* | ClickHouse |

### Data Source Awareness Guidelines
- **Trading queries** → Use DEX/PumpFun trading tables
- **Transfer queries** → Use token transfer tables (if available)
- **Holdings queries** → May require calculation across multiple tables`;

export const COMMON_KEYWORDS_MAPPING = `## Keywords to Data Type Mapping
**Trading Keywords** → Use trading tables:
- 买入, 卖出, 交易, 购买, buy, sell, trade, trading, purchase
- 价格, 成交, 市场, price, market, volume, trading volume

**Transfer Keywords** → Use transfer tables:
- 转入, 转出, 转账, 发送, 接收, 流向, 转移
- transfer, send, receive, move, flow

**Holdings Keywords** → Use holder tables:
- 余额, 持仓, 拥有, 持有者, 分布
- balance, holdings, owns, holders, distribution`;

/**
 * Get the complete blockchain terminology section for prompts
 */
export function getBlockchainTerminology(): string {
  return `${BLOCKCHAIN_BUSINESS_CONTEXT}

${DATABASE_ARCHITECTURE}

${BLOCKCHAIN_TERMINOLOGY}

${DATA_SOURCE_MAPPING}

${COMMON_KEYWORDS_MAPPING}`;
}

/**
 * Get just the terminology definitions without examples
 */
export function getTerminologyDefinitions(): string {
  return BLOCKCHAIN_TERMINOLOGY;
}

/**
 * Get keyword mapping for quick reference
 */
export function getKeywordMapping(): string {
  return COMMON_KEYWORDS_MAPPING;
}
