import { PGVectorStore } from "@langchain/community/vectorstores/pgvector";
import { GoogleGenerativeAIEmbeddings } from "@langchain/google-genai";

import { RAGError } from "../agent/errors.js";
import * as dotenv from "dotenv";

dotenv.config();

export interface RAGToolConfig {
  // Database connection configuration
  connectionString?: string;
  tableName?: string;
  
  // Retrieval configuration
  searchK?: number;
  similarityThreshold?: number;
  
  // Embedding model configuration
  embeddingModel?: string;
}

export interface RAGTool {
  name: string;
  description: string;
  invoke: (params: { query: string }) => Promise<string>;
}

// Simple cache to avoid re-initialization
const ragToolCache = new Map<string, RAGTool>();

/**
 * Initialize simple vector-based RAG tool
 * Only uses vector similarity search for simplicity and performance
 */
export async function initializeSimpleRAGTool(config: RAGToolConfig = {}): Promise<RAGTool | null> {
  const configKey = JSON.stringify(config);
  
  // Check cache first
  const cached = ragToolCache.get(configKey);
  if (cached) {
    console.log(`[RAG] ✅ Reusing cached RAG tool`);
    return cached;
  }

  try {
    console.log(`[RAG] Initializing simple vector RAG tool with Gemini embeddings...`);
    
    // Default configuration - using Gemini text-embedding-004
    const {
      connectionString = buildConnectionString(),
      tableName = "embeddings",
      searchK = 5,
      similarityThreshold = 0.4,
      embeddingModel = "text-embedding-004"
    } = config;

    console.log(`[RAG] Configuration:`, {
      tableName,
      searchK,
      similarityThreshold,
      embeddingModel: `Gemini ${embeddingModel}`
    });

    // Initialize Gemini embedding model
    const embeddings = new GoogleGenerativeAIEmbeddings({
      model: embeddingModel,
    });

    // Initialize vector store
    const vectorStore = await PGVectorStore.initialize(embeddings, {
      postgresConnectionOptions: {
        connectionString,
      },
      tableName,
      columns: {
        idColumnName: "id",
        vectorColumnName: "embedding",
        contentColumnName: "vector_id",
        metadataColumnName: "metadata",
      },
    });

    // Create simple RAG tool
    const ragTool: RAGTool = {
      name: "simple_vector_rag",
      description: `Simple vector-based RAG tool using Gemini ${embeddingModel} with similarity threshold >= ${similarityThreshold}`,
      invoke: async ({ query }: { query: string }) => {
        try {
          console.log(`[RAG] Processing query with Gemini embeddings: "${query}"`);
          
          // Use vector search with similarity scores
          const results = await vectorStore.similaritySearchWithScore(query, searchK);
          console.log(`[RAG] Vector search returned ${results.length} results`);
          
          // Filter by similarity threshold
          const filteredResults = results.filter(([_, score]) => score >= similarityThreshold);
          console.log(`[RAG] ${filteredResults.length} results passed similarity threshold ${similarityThreshold}`);
          
          if (filteredResults.length === 0) {
            console.log(`[RAG] ⚠️ No results passed similarity threshold`);
            return "";
          }
          
          // Extract documents and join content
          const documents = filteredResults.map(([doc, score]) => {
            console.log(`[RAG] - Document similarity: ${score.toFixed(3)}`);
            return doc.pageContent;
          });
          
          const resultText = documents.join('\n\n---\n\n');
          console.log(`[RAG] ✅ Returning ${documents.length} relevant documents`);
          
          return resultText;
        } catch (error) {
          console.error(`[RAG] ❌ Search error:`, error);
          throw new RAGError(`Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }
    };

    // Cache the tool
    ragToolCache.set(configKey, ragTool);

    console.log(`[RAG] ✅ Simple RAG tool initialized successfully with Gemini embeddings`);
    console.log(`[RAG] - Model: ${embeddingModel}, Search K: ${searchK}, Similarity threshold: ${similarityThreshold}`);

    return ragTool;
  } catch (error) {
    console.error(`[RAG] ❌ Initialization failed:`, (error as Error).message);
    return null;
  }
}

/**
 * Build default database connection string
 */
function buildConnectionString(): string {
  if (!process.env.PG_PASSWORD || !process.env.PG_USER || !process.env.PG_HOSTNAME || !process.env.PG_PORT || !process.env.PG_DATABASE) {
    throw new RAGError('Missing required PostgreSQL environment variables');
  }
  
  const encodedPassword = encodeURIComponent(process.env.PG_PASSWORD);
  return `postgresql://${process.env.PG_USER}:${encodedPassword}@${process.env.PG_HOSTNAME}:${process.env.PG_PORT}/${process.env.PG_DATABASE}`;
}

/**
 * Clear RAG cache (useful for testing or re-initialization)
 */
export function clearRAGCache(): void {
  ragToolCache.clear();
  console.log(`[RAG] Cache cleared`);
}

/**
 * Get RAG status information
 */
export function getRAGStatus(): {
  isInitialized: boolean;
  cacheSize: number;
} {
  return {
    isInitialized: ragToolCache.size > 0,
    cacheSize: ragToolCache.size,
  };
} 