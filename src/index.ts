/**
 * Text2SQL Plan-and-Execute System
 * Main entry point
 */
export {
  executeText2SQL,
  executeText2SQLPlan,
  createText2SQLPlanWorkflow,
  text2sqlGraph,
} from "./agent/text2sqlAgent.js";

export { Text2SQLPlanState, ExecutionStep } from "./agent/state.js";

export { getDataBySql } from "./utils/index.js";

export {
  generateTEXT2SQLPrompt,
  TEXT2SQL_PROMPT,
} from "./prompts/text2sqlPrompt.js";

export {
  PLAN_TEXT2SQL_PROMPT,
  REPLAN_TEXT2SQL_PROMPT,
  DATA_ANALYSIS_PROMPT,
} from "./prompts/planExecutePrompts.js";

// Schema and formatting utilities (minimal exposure)
export {
  TABLE_MAPPING,
  QUERY_RULES,
  formatTableListForPlanning,
  getTableNames,
} from "./config/schemaFormatter.js";

// Convenience exports
export { executeText2SQL as text2sql } from "./agent/text2sqlAgent.js";
