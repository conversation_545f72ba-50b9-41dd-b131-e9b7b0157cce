import { z } from "zod";
import { createRoute } from "@hono/zod-openapi";

// Security Schemas
export const HubbleAPIKeySchema = z.object({
  "HUBBLE-API-Key": z.string().describe("Hubble API Key for authentication"),
});

// Common Response Schemas
export const ErrorResponseSchema = z.object({
  error: z.string().describe("Error message"),
  timestamp: z.string().describe("Timestamp of the error"),
});

export const HealthResponseSchema = z.object({
  status: z.string().describe("Service status"),
  message: z.string().describe("Status message"),
  timestamp: z.string().describe("Timestamp of the response"),
});

// Text2SQL Request and Response Schemas
export const Text2SQLRequestSchema = z.object({
  query: z.string().min(1).describe("The user's natural language query"),
  stream: z.boolean().optional().default(true).describe("Whether to return streaming response (default: true)"),
});

export const Text2SQLResponseSchema = z.object({
  data: z.array(z.any()).describe("Query execution results"),
  sql: z.string().optional().describe("Generated SQL query"),
  timestamp: z.string().describe("Timestamp of the response"),
});

// Chart Generation Request and Response Schemas
export const ChartGenerationRequestSchema = z.object({
  query: z.string().min(1).describe("The user's natural language query"),
  stream: z.boolean().optional().default(true).describe("Whether to return streaming response (default: true)"),
});

export const ChartResultSchema = z.object({
  chartType: z.string().describe("The type of chart generated"),
  htmlCode: z.string().describe("Complete HTML code for the chart"),
  previewUrl: z.string().optional().describe("URL to preview the chart"),
  dataInsights: z.array(z.string()).describe("Insights about the data visualization"),
  generationTime: z.number().describe("Time taken to generate the chart in milliseconds"),
});

export const ChartGenerationResponseSchema = z.object({
  sql: z.string().describe("Generated SQL query"),
  data: z.any().describe("Query execution results"),
  chart: ChartResultSchema.describe("Generated chart information"),
  timestamp: z.string().describe("Timestamp of the response"),
});

// Separate SSE Event Schemas
export const Text2SQLSSEEventSchema = z.object({
  phase: z.enum(["plan", "sql_generation", "data_display", "error"]).describe("The current phase being executed"),
  status: z.enum(["start", "end"]).optional().describe("Phase status - start or end (not present for error events)"),
  plan: z.array(z.string()).optional().describe("The execution plan steps (included in plan phase end event)"),
  rowCount: z.number().optional().describe("Number of rows returned from SQL execution (included in sql_generation phase end event)"),
  duration: z.number().optional().describe("Phase execution duration in milliseconds (included in phase end events)"),
  data: z.any().optional().describe("The final result data (included in data_display phase end event)"),
  originalRowCount: z.number().optional().describe("Original number of rows before limiting (included in data_display phase end event)"),
  errors: z.array(z.string()).optional().describe("Error messages (for error events)"),
  timestamp: z.string().describe("Timestamp of the event"),
  message: z.string().describe("Message associated with the event"),
});

export const ChartSSEEventSchema = z.object({
  phase: z.enum(["plan", "sql_generation", "data_display", "chart_display", "error"]).describe("The current phase being executed"),
  status: z.enum(["start", "end"]).optional().describe("Phase status - start or end (not present for error events)"),
  plan: z.array(z.string()).optional().describe("The execution plan steps (included in plan phase end event)"),
  rowCount: z.number().optional().describe("Number of rows returned from SQL execution (included in sql_generation phase end event)"),
  sqlQuery: z.string().optional().describe("Generated SQL query (included in sql_generation phase end event)"),
  data: z.any().optional().describe("The SQL result data (included in data_display phase end event)"),
  originalRowCount: z.number().optional().describe("Original number of rows before limiting (included in data_display phase end event)"),
  chartType: z.string().optional().describe("Type of chart generated (included in chart_display phase end event)"),
  htmlCode: z.string().optional().describe("Complete HTML code for the chart (included in chart_display phase end event)"),
  generationTime: z.number().optional().describe("Time taken for chart generation in milliseconds (included in chart_display phase end event)"),
  dataRows: z.number().optional().describe("Number of data rows used for chart (included in chart_display phase end event)"),
  duration: z.number().optional().describe("Phase execution duration in milliseconds (included in phase end events)"),
  error: z.string().optional().describe("Error message (for error events)"),
  timestamp: z.string().describe("Timestamp of the event"),
  message: z.string().describe("Message associated with the event"),
});

// API Route Definitions
export const healthRoute = createRoute({
  method: "get",
  path: "/status",
  summary: "Health Check",
  description: "Checks if the service is running correctly.",
  tags: ["System"],
  ...(process.env.NODE_ENV === "production" ? {
    security: [
      {
        "HUBBLE-API-Key": [],
      },
    ],
  } : {}),
  responses: {
    200: {
      content: {
        "application/json": {
          schema: HealthResponseSchema,
        },
      },
      description: "Service is running normally.",
    },
  },
});

export const text2sqlRoute = createRoute({
  method: "post", 
  path: "/text2sql",
  summary: "Text2SQL Conversion",
  description: `
Converts a natural language query into an SQL query and executes it using a phase-based approach.

**Authentication:**
- Requires \`HUBBLE-API-Key\` header

**Streaming vs Regular Response:**
- Set \`stream: true\` (default) for Server-Sent Events (SSE) streaming
- Set \`stream: false\` for regular JSON response

### Three-Phase Processing:
1. **Plan Phase**: Creates an execution plan with multiple steps
2. **SQL Generation Phase**: Executes SQL queries based on the plan
3. **Data Display Phase**: Formats and returns the final results

### Simplified Streaming Event Structure:
The streaming response uses a clean, simplified event structure with only phase transitions:

**Phase Events:**
- Phase started: { phase: "plan", status: "start", message: "plan phase started", timestamp: "..." }
- Phase completed with data: { phase: "plan", status: "end", plan: [...], duration: 1200, message: "plan phase completed", timestamp: "..." }

**Error Events:**
- Error occurred: { phase: "error", errors: [...], message: "Error occurred: ...", timestamp: "..." }

### Event Fields:
- **phase**: Phase name ("plan", "sql_generation", "data_display") or "error"
- **status**: Phase status - "start" or "end" (not present for error events)
- **plan**: Array of execution steps (included in plan phase end event)
- **rowCount**: Number of rows returned from SQL execution (included in sql_generation phase end event)
- **data**: The final result data (included in data_display phase end event)
- **originalRowCount**: Original number of rows before limiting (included in data_display phase end event)
- **duration**: Phase execution time in milliseconds (included in phase end events)
- **errors**: Error messages (for error events)
- **timestamp**: ISO timestamp of the event
- **message**: Human-readable message describing the event

### Usage Examples:

**Streaming Response:**
\`\`\`javascript
fetch('/agent/api/v1/text2sql', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'HUBBLE-API-Key': 'your-api-key'
  },
  body: JSON.stringify({ 
    query: 'Show me the top 10 token trades by volume today',
    stream: true // default
  })
}).then(response => {
  const reader = response.body.getReader();
  // Process streaming data
});
\`\`\`

**Regular Response:**
\`\`\`javascript
fetch('/agent/api/v1/text2sql', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'HUBBLE-API-Key': 'your-api-key'
  },
  body: JSON.stringify({ 
    query: 'Show me the top 10 token trades by volume today',
    stream: false
  })
}).then(response => response.json());
\`\`\`

### Example Event Flow:
{ phase: "plan", status: "start" } → 
{ phase: "plan", status: "end", plan: [...], duration: 1200 } → 
{ phase: "sql_generation", status: "start" } → 
{ phase: "sql_generation", status: "end", rowCount: 10, duration: 3400 } → 
{ phase: "data_display", status: "start" } → 
{ phase: "data_display", status: "end", data: [...], rowCount: 10, originalRowCount: 10, duration: 100 }
  `,
  tags: ["Text2SQL"],
  ...(process.env.NODE_ENV === "production" ? {
    security: [
      {
        "HUBBLE-API-Key": [],
      },
    ],
  } : {}),
  request: {
    body: {
      content: {
        "application/json": {
          schema: Text2SQLRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "text/event-stream": {
          schema: z.object({
            data: Text2SQLSSEEventSchema.describe("SSE event data"),
          }),
        },
        "application/json": {
          schema: Text2SQLResponseSchema,
        },
      },
      description: "Returns either streaming events or JSON response based on stream parameter.",
    },
    400: {
      content: {
        "application/json": {
          schema: ErrorResponseSchema,
        },
      },
      description: "Invalid request parameters.",
    },
    500: {
      content: {
        "application/json": {
          schema: ErrorResponseSchema,
        },
      },
      description: "Internal server error.",
    },
  },
});

export const chartGenerationRoute = createRoute({
  method: "post",
  path: "/generate-chart",
  summary: "Chart Generation",
  description: `
Generates interactive charts from natural language queries. First converts the query to SQL, executes it, then creates a chart from the data.

**Authentication:**
- Requires \`HUBBLE-API-Key\` header

**Streaming vs Regular Response:**
- Set \`stream: true\` (default) for Server-Sent Events (SSE) streaming
- Set \`stream: false\` for regular JSON response

### AI Chart Selection:
The system automatically analyzes your data and query to select the most appropriate chart type:
- **Line charts** for time series and trend data
- **Bar charts** for categorical comparisons
- **Pie charts** for proportional data
- **Scatter plots** for correlation analysis
- **Area charts** for cumulative data

### Four-Phase Processing:
1. **Plan Phase**: Analyzes the query and creates an execution plan for chart generation
2. **SQL Generation Phase**: Executes SQL queries to retrieve the required data
3. **Data Display Phase**: Processes and prepares the data for visualization
4. **Chart Display Phase**: Generates the final interactive chart and renders it

### Streaming Event Structure:
Events use a simplified phase-based structure:

**Phase Events:**
- Phase started: { phase: "plan", status: "start", message: "..." }
- Phase completed: { phase: "plan", status: "end", plan: [...], duration: 1200, message: "..." }

**Four Phases:**
- **plan**: Query analysis and plan creation
  - End event includes: plan array with execution steps
- **sql_generation**: SQL execution and data retrieval  
  - End event includes: rowCount, sqlQuery
- **data_display**: Data processing and preparation
  - End event includes: data, rowCount, originalRowCount
- **chart_display**: Chart generation and final rendering
  - End event includes: chartType, htmlCode, generationTime, dataRows

**Error Events:**
- Error occurred: { phase: "current_phase", status: "error", error: "...", message: "..." }

### Usage Examples:

**Streaming Response:**
\`\`\`javascript
fetch('/agent/api/v1/generate-chart', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'HUBBLE-API-Key': 'your-api-key'
  },
  body: JSON.stringify({ 
    query: 'Show token price trends over the last 30 days',
    stream: true // default
  })
}).then(response => {
  const reader = response.body.getReader();
  // Process streaming data
});
\`\`\`

**Regular Response:**
\`\`\`javascript
fetch('/agent/api/v1/generate-chart', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'HUBBLE-API-Key': 'your-api-key'
  },
  body: JSON.stringify({ 
    query: 'Show token price trends over the last 30 days',
    stream: false
  })
}).then(response => response.json());
\`\`\`
  `,
  tags: ["Charts"],
  ...(process.env.NODE_ENV === "production" ? {
    security: [
      {
        "HUBBLE-API-Key": [],
      },
    ],
  } : {}),
  request: {
    body: {
      content: {
        "application/json": {
          schema: ChartGenerationRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "text/event-stream": {
          schema: z.object({
            data: ChartSSEEventSchema.describe("SSE event data"),
          }),
        },
        "application/json": {
          schema: ChartGenerationResponseSchema,
        },
      },
      description: "Returns either streaming events or JSON response based on stream parameter.",
    },
    400: {
      content: {
        "application/json": {
          schema: ErrorResponseSchema,
        },
      },
      description: "Invalid request parameters.",
    },
    500: {
      content: {
        "application/json": {
          schema: ErrorResponseSchema,
        },
      },
      description: "Internal server error.",
    },
  },
}); 