import { OpenAPIHono } from "@hono/zod-openapi";
import { cors } from "hono/cors";
import { serve } from "@hono/node-server";
import { swaggerUI } from "@hono/swagger-ui";
import { serveStatic } from "@hono/node-server/serve-static";
import { 
  healthRoute, 
  text2sqlRoute,
  chartGenerationRoute
} from "./apiSchema.js";
import { handleText2SQL } from "./text2sqlRoutes.js";
import { handleChartGeneration } from "./chartRoutes.js";
import * as dotenv from "dotenv";

dotenv.config();

const app = new OpenAPIHono();
const v1 = new OpenAPIHono();

const PORT = process.env.PORT ? parseInt(process.env.PORT, 10) : 3000;

// Middleware
v1.use("*", cors());

// Generate base OpenAPI spec
const baseOpenAPISpec = {
  openapi: "3.0.0",
  info: {
    version: "1.0.0",
    title: "Hubble AI Text2SQL API",
    description: `
# Hubble AI Text2SQL API

An intelligent Text2SQL conversion service and chart generation platform based on LangGraph.

## Features

- **Text2SQL Conversion**: Convert natural language queries to SQL and execute them
- **Chart Generation**: Generate interactive charts from natural language queries
- **Streaming Support**: Real-time streaming responses for both services
- **Flexible Response Format**: Choose between streaming and regular JSON responses

${process.env.NODE_ENV === "production" ? `## Authentication

All API endpoints require authentication using the \`HUBBLE-API-Key\` header:

\`\`\`bash
curl -X 'POST' \\
  'https://api.hubble-rpc.xyz/agent/api/v1/text2sql' \\
  -H 'accept: */*' \\
  -H 'HUBBLE-API-Key: XXXXXXXXXXXXXX' \\
  -H 'Content-Type: application/json'
\`\`\`` : `## Development Mode

No authentication required for development environment.`}

## Getting Started

All APIs support both streaming and non-streaming modes:
- Set \`stream: true\` for real-time Server-Sent Events (default)
- Set \`stream: false\` for regular JSON responses
    `,
  },
  servers: [
    {
      url: process.env.NODE_ENV === "production" 
        ? "https://api.hubble-rpc.xyz/agent/api/v1" 
        : `http://localhost:${PORT}/agent/api/v1`,
      description: process.env.NODE_ENV === "production" ? "Production" : "Development",
    },
    
  ],
};

// Custom OpenAPI route with security scheme
v1.get("/openapi.json", (c) => {
  // Get the generated OpenAPI spec
  const openApiSpec = v1.getOpenAPI31Document(baseOpenAPISpec);
  
  // Add security scheme definition only in production
  if (process.env.NODE_ENV === "production") {
    openApiSpec.components = openApiSpec.components || {};
    openApiSpec.components.securitySchemes = {
      "HUBBLE-API-Key": {
        type: "apiKey",
        in: "header",
        name: "HUBBLE-API-Key",
        description: "API key for accessing Hubble services",
      },
    };
  }
  
  return c.json(openApiSpec);
});

// Official Swagger UI Documentation
v1.get(
  "/docs",
  swaggerUI({
    url: "./openapi.json",
  })
);

// Health check route
v1.openapi(healthRoute, (c) => {
  return c.json({
    status: "ok",
    message: "Hubble AI Text2SQL API is running",
    timestamp: new Date().toISOString(),
    features: {
      text2sql: "Available",
      chartGeneration: "Available",
      streaming: "Supported",
    },
  });
});

// Text2SQL route
v1.openapi(text2sqlRoute, handleText2SQL);

// Chart generation route (functional, but not in docs)
v1.openapi(chartGenerationRoute, handleChartGeneration);

// Mount API routes
app.route("/agent/api/v1", v1);

// Serve static files (for chart previews, etc.)
app.use("/*", serveStatic({ root: "./public" }));

// Start server
console.log(`🚀 Starting Hubble AI Text2SQL API Server...`);
console.log(`📊 Features: Text2SQL Conversion, Chart Generation`);
console.log(`🔄 Streaming: Supported`);
console.log(`📖 Documentation: http://localhost:${PORT}/agent/api/v1/docs`);
console.log(`🏥 Health Check: http://localhost:${PORT}/agent/api/v1/status`);

serve({
  fetch: app.fetch,
  port: PORT,
});

console.log(`✅ Server running on http://localhost:${PORT}`);
console.log(`🌐 API Base URL: http://localhost:${PORT}/agent/api/v1`);
console.log(`📚 API Documentation: http://localhost:${PORT}/agent/api/v1/docs`);

export default app;

