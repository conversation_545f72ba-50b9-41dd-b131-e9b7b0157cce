import { type Context } from "hono";
import { streamSSE, type SSEStreaming<PERSON>pi } from "hono/streaming";
import { BaseCallbackHandler } from "@langchain/core/callbacks/base";
import { createChartAgent } from "../agent/chartAgent.js";
import { ChartGenerationRequestSchema } from "./apiSchema.js";

/**
 * Execute Chart Generation with streaming support
 */
async function executeChartGenerationWithStreaming(
  userQuery: string,
  stream: SSEStreamingApi,
) {
  let currentPhase = "";
  let phaseStartTime = 0;
  let finalChartData: any = null;

  try {

    // Helper function to handle phase transitions
    const handlePhaseTransition = async (nodeName: string, stateData?: any) => {
      let newPhase = "";

      // Map chart nodes to phases
      if (nodeName === "text2sql") {
        newPhase = "sql_generation";
      } else if (nodeName === "data_display") {
        newPhase = "data_display";
      } else if (nodeName === "chart_generation") {
        newPhase = "chart_display";
      }

      // Phase transition logic
      if (newPhase && newPhase !== currentPhase) {
        // End previous phase
        if (currentPhase) {
          const phaseEndData: any = {
            phase: currentPhase,
            status: "end",
            duration: Date.now() - phaseStartTime,
            message: `${currentPhase} phase completed`,
            timestamp: new Date().toISOString(),
          };

          // Add phase-specific data
          if (currentPhase === "sql_generation" && stateData?.sqlResults) {
            phaseEndData.rowCount = stateData.sqlResults.length;
            phaseEndData.sqlQuery = stateData.sqlQuery;
          } else if (currentPhase === "data_display" && stateData?.sqlResults) {
            phaseEndData.data = stateData.sqlResults;
            phaseEndData.rowCount = stateData.sqlResults.length;
            phaseEndData.originalRowCount = stateData.sqlResults.length;
          }

          await stream.writeSSE({
            data: JSON.stringify(phaseEndData)
          });
        }

        // Start new phase
        currentPhase = newPhase;
        phaseStartTime = Date.now();
        
        await stream.writeSSE({
          data: JSON.stringify({
            phase: currentPhase,
            status: "start",
            message: `${currentPhase} phase started`,
            timestamp: new Date().toISOString(),
          })
        });
      }
    };

    // Initialize chart agent
    const chartAgent = createChartAgent();

    // Start with plan phase
    currentPhase = "plan";
    phaseStartTime = Date.now();
    
    await stream.writeSSE({
      data: JSON.stringify({
        phase: "plan",
        status: "start",
        message: "plan phase started",
        timestamp: new Date().toISOString(),
      })
    });

    // Execute chart generation with streaming
    const chartStream = await chartAgent.stream(
      userQuery,
      undefined, // schema
      {}
    );

    for await (const event of chartStream) {
      const nodeNames = Object.keys(event);
      const stateData = Object.values(event)[0] as any;
      const nodeName = nodeNames[0];

      // Handle phase transitions
      await handlePhaseTransition(nodeName, stateData);

      // Handle special cases and phase-specific logic
      if (nodeName === "text2sql" && stateData.sqlResults) {
        // After text2sql completes, transition to data_display
        if (currentPhase === "sql_generation") {
          // End sql_generation phase
          await stream.writeSSE({
            data: JSON.stringify({
              phase: "sql_generation",
              status: "end",
              rowCount: stateData.sqlResults.length,
              sqlQuery: stateData.sqlQuery,
              duration: Date.now() - phaseStartTime,
              message: "sql_generation phase completed",
              timestamp: new Date().toISOString(),
            })
          });

          // Start data_display phase
          currentPhase = "data_display";
          phaseStartTime = Date.now();

          await stream.writeSSE({
            data: JSON.stringify({
              phase: "data_display",
              status: "start",
              message: "data_display phase started",
              timestamp: new Date().toISOString(),
            })
          });
        }
      }

      // Handle data_display phase completion
      if (nodeName === "data_display" && stateData.sqlResults) {
        if (currentPhase === "data_display") {
          // Limit data similar to text2sqlAgent
          const MAX_ROWS_LIMIT = 1000;
          const resultData = stateData.sqlResults || [];
          const originalRowCount = Array.isArray(resultData) ? resultData.length : 0;
          const limitedData = Array.isArray(resultData)
            ? resultData.slice(0, MAX_ROWS_LIMIT)
            : [];

          // End data_display phase with data
          await stream.writeSSE({
            data: JSON.stringify({
              phase: "data_display",
              status: "end",
              data: limitedData,
              rowCount: limitedData.length,
              originalRowCount: originalRowCount,
              duration: Date.now() - phaseStartTime,
              message: "data_display phase completed",
              timestamp: new Date().toISOString(),
            })
          });
        }
      }

      // Handle plan completion (transition from plan to sql_generation)
      if (currentPhase === "plan" && (nodeName === "text2sql" || stateData.plan)) {
        // End plan phase
        await stream.writeSSE({
          data: JSON.stringify({
            phase: "plan",
            status: "end",
            plan: stateData.plan || ["Generate chart for user query"],
            duration: Date.now() - phaseStartTime,
            message: "plan phase completed",
            timestamp: new Date().toISOString(),
          })
        });

        // Start sql_generation phase
        currentPhase = "sql_generation";
        phaseStartTime = Date.now();
        
        await stream.writeSSE({
          data: JSON.stringify({
            phase: "sql_generation",
            status: "start",
            message: "sql_generation phase started",
            timestamp: new Date().toISOString(),
          })
        });
      }

      // Check for completion
      if (stateData.isComplete || stateData.chartResult) {
        finalChartData = stateData;
        
        // End data_display phase if we haven't already
        if (currentPhase === "data_display") {
          await stream.writeSSE({
            data: JSON.stringify({
              phase: "data_display",
              status: "end",
              data: stateData.sqlResults || [],
              rowCount: stateData.sqlResults?.length || 0,
              originalRowCount: stateData.sqlResults?.length || 0,
              duration: Date.now() - phaseStartTime,
              message: "data_display phase completed",
              timestamp: new Date().toISOString(),
            })
          });
        }

        // Start and end chart_display phase
        currentPhase = "chart_display";
        phaseStartTime = Date.now();
        
        await stream.writeSSE({
          data: JSON.stringify({
            phase: "chart_display",
            status: "start",
            message: "chart_display phase started",
            timestamp: new Date().toISOString(),
          })
        });

        const chartResult = stateData.chartResult;
        
        await stream.writeSSE({
          data: JSON.stringify({
            phase: "chart_display",
            status: "end",
            chartType: chartResult?.chartType || "unknown",
            htmlCode: chartResult?.htmlCode || "",
            generationTime: chartResult?.generationTime || 0,
            dataRows: stateData.sqlResults?.length || 0,
            sqlQuery: stateData.sqlQuery || "",
            duration: Date.now() - phaseStartTime,
            message: "chart_display phase completed",
            timestamp: new Date().toISOString(),
          })
        });
        
        break;
      }
    }

  } catch (error) {
    console.error("[Chart Streaming] Error:", error);
    await stream.writeSSE({
      data: JSON.stringify({
        phase: currentPhase || "unknown",
        status: "error",
        error: error instanceof Error ? error.message : "Unknown error",
        message: "Chart generation failed",
        timestamp: new Date().toISOString(),
      })
    });
  }
}

/**
 * Execute Chart Generation without streaming (regular JSON response)
 */
async function executeChartGenerationRegular(userQuery: string) {
  try {
    const chartAgent = createChartAgent();
    const startTime = Date.now();

    console.log(`🎨 [Chart] Generating chart for query: ${userQuery}`);

    // Generate chart using the chart agent
    const result = await chartAgent.execute(
      userQuery,
      undefined, // schema
    );

    const processingTime = Date.now() - startTime;
    console.log(`✅ [Chart] Completed in ${processingTime}ms`);

    // Extract chart result
    const chartResult = result.chartResult;

    if (!chartResult) {
      throw new Error("Chart generation failed - no chart result");
    }

    return {
      sql: result.sqlQuery,
      data: result.sqlResults,
      chart: {
        chartType: chartResult.chartType,
        htmlCode: chartResult.htmlCode,
        generationTime: chartResult.generationTime,
        dataInsights: [], // Can be added later
        previewUrl: undefined, // Can be added later
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error(`❌ [Chart] Error:`, error);
    throw error;
  }
}

/**
 * Chart generation route handler
 */
export const handleChartGeneration = async (c: Context) => {
  const body = await c.req.json();
  const validation = ChartGenerationRequestSchema.safeParse(body);

  if (!validation.success) {
    return c.json({
      error: "Invalid request parameters",
      details: validation.error.issues,
      timestamp: new Date().toISOString(),
    }, 400);
  }

  const { query, stream = true } = validation.data;
  console.log(`🌐 [Chart API] Query: ${query}, Stream: ${stream}`);

  try {
    if (stream) {
      // Return streaming response
      return streamSSE(c, async (stream: SSEStreamingApi) => {
        await executeChartGenerationWithStreaming(query, stream);
      });
    } else {
      // Return regular JSON response
      const result = await executeChartGenerationRegular(query);
      return c.json(result);
    }
  } catch (error) {
    console.error(`❌ [Chart API] Error:`, error);
    return c.json({
      error: error instanceof Error ? error.message : "Chart generation failed",
      timestamp: new Date().toISOString(),
    }, 500);
  }
}; 