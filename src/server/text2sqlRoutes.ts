import { type Context } from "hono";
import { streamSSE, type SSEStreaming<PERSON><PERSON> } from "hono/streaming";
import { createText2SQLAgent } from "../agent/text2sqlAgent.js";
import { Text2SQLRequestSchema } from "./apiSchema.js";

const MAX_ROWS_LIMIT = 50;

/**
 * Execute Text2SQL with streaming support
 */
async function executeText2SQLWithStreaming(
  query: string,
  stream: SSEStreamingApi,
) {
  try {
    let currentPhase = "";
    let phaseStartTime = 0;
    let planData: string[] = [];
    let executionRowCount = 0;

    // Helper function to handle phase transitions
    const handlePhaseTransition = async (nodeName: string, stateData?: any) => {
      let newPhase = "";
      
      // Map nodes to phases
      if (nodeName === "planner") {
        newPhase = "plan";
      } else if (nodeName === "rag" || nodeName === "executor") {
        newPhase = "sql_generation";
      } else if (nodeName === "completion") {
        newPhase = "data_display";
      }

      // Phase transition logic
      if (newPhase && newPhase !== currentPhase) {
        // End previous phase with data
        if (currentPhase) {
          const endEventData: any = {
            phase: currentPhase,
            status: "end",
            message: `${currentPhase} phase completed`,
            duration: Date.now() - phaseStartTime,
            timestamp: new Date().toISOString(),
          };

          // Add phase-specific data
          if (currentPhase === "plan" && planData.length > 0) {
            endEventData.plan = planData;
          } else if (currentPhase === "sql_generation" && executionRowCount > 0) {
            endEventData.rowCount = executionRowCount;
          }

          await stream.writeSSE({
            data: JSON.stringify(endEventData),
          });
        }

        // Start new phase
        currentPhase = newPhase;
        phaseStartTime = Date.now();
        
        await stream.writeSSE({
          data: JSON.stringify({
            phase: currentPhase,
            status: "start",
            message: `${currentPhase} phase started`,
            timestamp: new Date().toISOString(),
          }),
        });
      }
    };

    // Create the agent
    const agent = createText2SQLAgent();

    let finalResult = null;

    // Use the agent's stream method
    const streamResult = await agent.stream(query, undefined, {});

    // Process streaming events
    try {
      for await (const event of streamResult) {
        // Extract node name and data from the event
        const nodeNames = Object.keys(event);
        const nodeData = Object.values(event)[0];
        const nodeName = nodeNames[0];

        // Handle events based on node type
        if (nodeData && typeof nodeData === "object") {
          const stateData = nodeData as any;

          // Capture plan data when planner completes
          if (nodeName === "planner" && stateData.plan && stateData.plan.length > 0) {
            planData = stateData.plan;
          }

          // Capture execution row count when executor completes
          if (nodeName === "executor" && stateData.completedSteps && stateData.completedSteps.length > 0) {
            const lastStep = stateData.completedSteps[stateData.completedSteps.length - 1];
            if (lastStep.type === "query" && lastStep.output && lastStep.output.sql) {
              executionRowCount = lastStep.output.rowCount || 0;
            }
          }

          // Handle phase transitions
          await handlePhaseTransition(nodeName, stateData);

          // Send error events if needed
          if (stateData.errors && stateData.errors.length > 0) {
            await stream.writeSSE({
              data: JSON.stringify({
                phase: "error",
                errors: stateData.errors,
                message: `Error occurred: ${stateData.errors.join(', ')}`,
                timestamp: new Date().toISOString(),
              }),
            });
          }

          // Check for completion
          if (stateData.isComplete) {
            finalResult = stateData;
            break;
          }
        }
      }
    } catch (streamError) {
      console.error("[Streaming] Stream processing error:", streamError);
      await stream.writeSSE({
        data: JSON.stringify({
          phase: "error",
          error: streamError instanceof Error ? streamError.message : "Stream processing error",
          message: streamError instanceof Error ? streamError.message : "Stream processing error",
          timestamp: new Date().toISOString(),
        }),
      });
    }

    // End final phase with result data
    if (currentPhase) {
      const resultData = finalResult?.processedResults || [];
      const originalRowCount = Array.isArray(resultData) ? resultData.length : 0;
      const limitedData = Array.isArray(resultData)
        ? resultData.slice(0, MAX_ROWS_LIMIT)
        : [];

      const endEventData: any = {
        phase: currentPhase,
        status: "end",
        message: `${currentPhase} phase completed`,
        duration: Date.now() - phaseStartTime,
        timestamp: new Date().toISOString(),
      };

      // Add final result data for data_display phase
      if (currentPhase === "data_display") {
        endEventData.data = limitedData;
        endEventData.rowCount = limitedData.length;
        endEventData.originalRowCount = originalRowCount;
      }

      await stream.writeSSE({
        data: JSON.stringify(endEventData),
      });
    }
  } catch (error) {
    console.error("[Text2SQL Streaming] Error:", error);
    await stream.writeSSE({
      data: JSON.stringify({
        phase: "error",
        error: error instanceof Error ? error.message : "Unknown error in streaming",
        message: error instanceof Error ? error.message : "Unknown error in streaming",
        timestamp: new Date().toISOString(),
      }),
    });
  }
}

/**
 * Execute Text2SQL without streaming (regular JSON response)
 */
async function executeText2SQLRegular(query: string) {
  try {
    const agent = createText2SQLAgent();
    const startTime = Date.now();

    console.log(`🔍 [Text2SQL] Executing query: ${query}`);

    // Execute the query
    const data = await agent.execute(query);
    const originalRowCount = data.length;
    const limitedData = data.slice(0, MAX_ROWS_LIMIT);

    const processingTime = Date.now() - startTime;
    console.log(
      `✅ [Text2SQL] Completed in ${processingTime}ms, returning ${limitedData.length} of ${originalRowCount} rows`,
    );

    return {
      data: limitedData,
      rowCount: limitedData.length,
      originalRowCount: originalRowCount,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error(`❌ [Text2SQL] Error:`, error);
    throw error;
  }
}

/**
 * Text2SQL route handler
 */
export const handleText2SQL = async (c: Context) => {
  const body = await c.req.json();
  const validation = Text2SQLRequestSchema.safeParse(body);

  if (!validation.success) {
    return c.json({
      error: "Invalid request parameters",
      details: validation.error.issues,
      timestamp: new Date().toISOString(),
    }, 400);
  }

  const { query, stream = true } = validation.data;
  console.log(
    `🌐 [Text2SQL API] Query: ${query}, Stream: ${stream}, Limit: ${MAX_ROWS_LIMIT}`,
  );

  try {
    if (stream) {
      // Return streaming response
      return streamSSE(c, async (stream: SSEStreamingApi) => {
        await executeText2SQLWithStreaming(query, stream);
      });
    } else {
      // Return regular JSON response
      const result = await executeText2SQLRegular(query);
      return c.json(result);
    }
  } catch (error) {
    console.error(`❌ [Text2SQL API] Error:`, error);
    return c.json({
      error: error instanceof Error ? error.message : "Internal server error",
      timestamp: new Date().toISOString(),
    }, 500);
  }
}; 