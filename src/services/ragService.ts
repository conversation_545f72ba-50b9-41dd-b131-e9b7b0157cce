import {
  RAGTool,
  RAGToolConfig,
  initializeSimpleRAGTool,
} from "../tools/ragTool.js";
import { RAGError } from "../agent/errors.js";
import { withTimeout } from "../agent/utils.js";

export interface RAGServiceConfig extends RAGToolConfig {
  timeoutMs?: number;
  retries?: number;
}

export class RAGService {
  private ragTool: RAGTool | null = null;
  private config: RAGServiceConfig;
  private initializationPromise: Promise<RAGTool | null> | null = null;

  constructor(config: RAGServiceConfig = {}) {
    this.config = {
      timeoutMs: 15000, // Increased to 15 seconds for reliable RAG initialization and retrieval
      retries: 1, // Reduced from 2 for simpler logic
      searchK: 5, // Default search results
      similarityThreshold: 0.4,
      ...config,
    };
  }

  /**
   * Initialize RAG tool with proper error handling and retries
   */
  private async initializeRAGTool(): Promise<RAGTool | null> {
    if (this.ragTool) {
      return this.ragTool;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }

  private async performInitialization(): Promise<RAGTool | null> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= (this.config.retries || 1); attempt++) {
      try {
        console.log(`[RAG Service] Initialization attempt ${attempt}...`);

        const tool = await withTimeout(
          initializeSimpleRAGTool(this.config),
          this.config.timeoutMs!,
          "RAG tool initialization timed out",
        );

        if (tool) {
          this.ragTool = tool;
          console.log(
            `[RAG Service] ✅ Initialized successfully on attempt ${attempt}`,
          );
          return tool;
        } else {
          throw new RAGError("RAG tool initialization returned null");
        }
      } catch (error) {
        lastError =
          error instanceof Error
            ? error
            : new Error("Unknown initialization error");
        console.log(
          `[RAG Service] ❌ Attempt ${attempt} failed: ${lastError.message}`,
        );

        if (attempt < (this.config.retries || 1)) {
          // Wait before retry
          const delay = 1000;
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    console.log(`[RAG Service] ❌ All initialization attempts failed`);
    throw new RAGError(
      `RAG service initialization failed after ${this.config.retries} attempts`,
      { lastError: lastError?.message },
    );
  }

  /**
   * Retrieve relevant examples for a query
   */
  public async retrieveExamples(query: string): Promise<string> {
    if (!query || typeof query !== "string") {
      throw new RAGError("Invalid query provided to RAG service");
    }

    try {
      const tool = await this.initializeRAGTool();

      if (!tool) {
        console.log(
          `[RAG Service] ⚠️ RAG tool unavailable, returning empty result`,
        );
        return "";
      }

      console.log(`[RAG Service] Retrieving examples for query: "${query}"`);

      const result = await withTimeout(
        tool.invoke({ query }),
        this.config.timeoutMs!,
        "RAG retrieval timed out",
      );

      if (result && result.trim()) {
        console.log(`[RAG Service] ✅ Retrieved relevant examples`);
        return result;
      } else {
        console.log(`[RAG Service] ⚠️ No relevant examples found`);
        return "";
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown retrieval error";
      console.log(`[RAG Service] ❌ Retrieval failed: ${errorMessage}`);

      // Don't throw, return empty string to allow workflow to continue
      return "";
    }
  }

  /**
   * Check if RAG service is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      const tool = await this.initializeRAGTool();
      return tool !== null;
    } catch {
      return false;
    }
  }

  /**
   * Get service status information
   */
  public getStatus(): {
    initialized: boolean;
    config: RAGServiceConfig;
  } {
    return {
      initialized: this.ragTool !== null,
      config: this.config,
    };
  }

  /**
   * Reset the service (useful for testing or re-initialization)
   */
  public reset(): void {
    this.ragTool = null;
    this.initializationPromise = null;
    console.log(`[RAG Service] Service reset`);
  }
}
