import { ExecutionStep } from "../agent/state.js";
import { SQLExecutionResult, ResponseFormatting } from "../agent/types.js";
import { formatExecutionTime } from "../agent/utils.js";

export class FormatService {
  /**
   * Format a simple query response without LLM analysis
   */
  public formatSimpleQueryResponse(
    userQuery: string,
    sql: string,
    queryResult: unknown,
    rowCount: number,
  ): string {
    const timestamp = new Date().toLocaleString();

    let response = `✅ Query Executed Successfully\n\n`;
    response += `**Query:** ${userQuery}\n`;
    response += `**Executed At:** ${timestamp}\n`;
    response += `**Rows Returned:** ${rowCount}\n\n`;

    response += `**Generated SQL:**\n`;
    response += `\`\`\`sql\n${sql}\n\`\`\`\n\n`;

    if (rowCount > 0) {
      response += `**Query Results:**\n`;
      response += this.formatQueryResults(queryResult);
    } else {
      response += `ℹ️ No data was returned from the query.\n`;
    }

    response += `\n\n✨ This query was completed directly as a simple data retrieval task.`;

    return response;
  }

  /**
   * Format query results based on data type
   */
  private formatQueryResults(queryResult: unknown): string {
    if (Array.isArray(queryResult) && queryResult.length > 0) {
      const firstItem = queryResult[0];

      if (typeof firstItem === "object" && firstItem !== null) {
        return this.formatTableData(queryResult as Record<string, unknown>[]);
      } else {
        return this.formatArrayData(queryResult);
      }
    }

    // For non-array results
    return `\`\`\`json\n${JSON.stringify(queryResult, null, 2)}\n\`\`\``;
  }

  /**
   * Format array of objects as a table
   */
  private formatTableData(data: Record<string, unknown>[]): string {
    if (data.length === 0) return "";

    const headers = Object.keys(data[0]);
    const headerLine = `| ${headers.join(" | ")} |`;
    const separatorLine = `| ${headers.map(() => "---").join(" | ")} |`;

    let result = `${headerLine}\n${separatorLine}\n`;

    for (const row of data) {
      const rowValues = headers.map((header) => {
        const value = row[header];
        return this.formatCellValue(value);
      });
      result += `| ${rowValues.join(" | ")} |\n`;
    }

    return result;
  }

  /**
   * Format array data as numbered list
   */
  private formatArrayData(data: unknown[]): string {
    return data
      .map((item, index) => `${index + 1}. ${JSON.stringify(item, null, 2)}`)
      .join("\n");
  }

  /**
   * Format individual cell values for display
   */
  private formatCellValue(value: unknown): string {
    if (value === null || value === undefined) {
      return "NULL";
    }

    if (typeof value === "string") {
      return value.length > 50 ? `${value.substring(0, 47)}...` : value;
    }

    if (typeof value === "object") {
      return JSON.stringify(value);
    }

    return String(value);
  }

  /**
   * Format execution summary
   */
  public formatExecutionSummary(
    completedSteps: ExecutionStep[],
    totalExecutionTime: number,
  ): string {
    const successfulSteps = completedSteps.filter(
      (step) => step.status === "completed",
    );
    const failedSteps = completedSteps.filter(
      (step) => step.status === "failed",
    );

    let summary = `\n📊 **Execution Summary**\n`;
    summary += `- Total Steps: ${completedSteps.length}\n`;
    summary += `- Successful: ${successfulSteps.length}\n`;
    summary += `- Failed: ${failedSteps.length}\n`;
    summary += `- Total Time: ${formatExecutionTime(totalExecutionTime)}\n`;

    if (failedSteps.length > 0) {
      summary += `\n❌ **Failed Steps:**\n`;
      for (const step of failedSteps) {
        summary += `- ${step.description}: ${step.error}\n`;
      }
    }

    return summary;
  }

  /**
   * Format step execution details
   */
  public formatStepDetails(step: ExecutionStep): string {
    let details = `**Step ${step.id}:** ${step.description}\n`;
    details += `**Type:** ${step.type}\n`;
    details += `**Status:** ${step.status}\n`;

    if (step.executionTime) {
      details += `**Execution Time:** ${formatExecutionTime(step.executionTime)}\n`;
    }

    if (step.status === "failed" && step.error) {
      details += `**Error:** ${step.error}\n`;
    }

    if (step.output && step.type === "query") {
      const queryOutput = step.output as SQLExecutionResult;
      details += `**Rows Returned:** ${queryOutput.rowCount}\n`;
      details += `**RAG Enhanced:** ${queryOutput.ragEnhanced ? "Yes" : "No"}\n`;
    }

    return details;
  }

  /**
   * Create response formatting metadata
   */
  public createResponseMetadata(
    hasResults: boolean,
    resultsCount: number,
  ): ResponseFormatting {
    return {
      timestamp: new Date().toISOString(),
      queryExecuted: hasResults,
      resultsCount,
    };
  }
}
