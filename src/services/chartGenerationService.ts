import { LLMService } from "./llmService.js";
import { RunnableConfig } from "@langchain/core/runnables";

export interface ChartGenerationRequest {
  data: any[];
  userQuery: string;
}

export interface ChartGenerationResult {
  chartType: string;
  htmlCode: string;
  generationTime: number;
}

/**
 * 独立的图表生成服务
 * 完全与Text2SQL解耦，只负责根据数据生成图表
 * 使用ECharts作为图表渲染引擎
 */
export class ChartGenerationService {
  private llmService: LLMService;

  constructor(llmService: LLMService) {
    this.llmService = llmService;
  }

  /**
   * 生成图表
   */
  async generateChart(request: ChartGenerationRequest, config?: RunnableConfig): Promise<ChartGenerationResult> {
    const startTime = Date.now();
    
    try {
      // 分析数据样本
      const dataSample = request.data.slice(0, 10);
      const dataStructure = this.analyzeDataStructure(request.data);
      
      // 让LLM生成图表配置模板（不包含实际数据）
      const chartResult = await this.generateChartWithLLM(
        dataSample,
        dataStructure,
        request.userQuery,
        config
      );
      
      // 生成完整HTML，动态注入实际数据
      const htmlCode = this.generateHTML(chartResult.config, chartResult.chartType, request.data, dataStructure);
      
      return {
        chartType: chartResult.chartType,
        htmlCode,
        generationTime: Date.now() - startTime,
      };
      
    } catch (error) {
      console.error("Chart generation failed:", error);
      throw new Error(`Chart generation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 分析数据结构
   */
  private analyzeDataStructure(data: any[]) {
    if (!data || data.length === 0) {
      return { columns: [], rowCount: 0, sample: [] };
    }

    const firstRow = data[0];
    const columns = Object.keys(firstRow);
    const sample = data.slice(0, 5);
    
    return {
      columns,
      rowCount: data.length,
      sample,
    };
  }

  /**
   * 使用LLM生成图表
   */
  private async generateChartWithLLM(
    dataSample: any[],
    dataStructure: any,
    userQuery: string,
    config?: RunnableConfig
  ) {
    // 只传递前3行样本数据给LLM以减少token消耗
    const smallSample = dataSample.slice(0, 3);
    const prompt = this.createChartGenerationPrompt(
      smallSample,
      dataStructure,
      userQuery
    );

    const response = await this.llmService.invoke(
      "You are a data visualization expert. Generate ECharts configuration template based on the data structure.",
      prompt,
      config
    );

    return this.parseChartResponse(response.content, smallSample);
  }

  /**
   * 创建图表生成提示词
   */
  private createChartGenerationPrompt(
    dataSample: any[],
    dataStructure: any,
    userQuery: string,
  ): string {
    return `
Based on the following data and user query, generate a complete ECharts configuration:

## Data Sample:
${JSON.stringify(dataSample, null, 2)}

## Data Structure:
- Columns: ${dataStructure.columns.join(', ')}
- Row Count: ${dataStructure.rowCount}

## User Query: 
"${userQuery}"

## Chart Type Selection: 
Automatically choose the most appropriate chart type based on data analysis

## Instructions:
1. Analyze the data structure and determine the most appropriate chart type
2. Generate an ECharts configuration TEMPLATE (DO NOT include actual data)
3. Use data structure information to determine appropriate field mappings
4. Make it interactive with tooltips and legend
5. Use responsive design
6. IMPORTANT: Do NOT include formatter functions - use simple configurations only
7. IMPORTANT: Use "__DATA_PLACEHOLDER__" for series data and "__CATEGORIES_PLACEHOLDER__" for xAxis categories

## Response Format:
Return a JSON object with:
{
  "chartType": "selected_chart_type",
  "config": {
    // ECharts configuration template
    "title": { "text": "...", "left": "center" },
    "tooltip": { "trigger": "axis" },
    "legend": { "data": ["..."], "bottom": "0%" },
    "grid": { "left": "3%", "right": "4%", "bottom": "10%", "containLabel": true },
    "xAxis": { "type": "category", "data": "__CATEGORIES_PLACEHOLDER__", "name": "column_name" },
    "yAxis": { "type": "value", "name": "column_name" },
    "series": [ { "name": "...", "type": "bar", "data": "__DATA_PLACEHOLDER__" } ]
  },
  "dataMapping": {
    "xColumn": "column_name_for_x_axis",
    "yColumn": "column_name_for_y_axis"
  }
}

Generate the chart configuration now:
`;
  }

  /**
   * 解析LLM响应
   */
  private parseChartResponse(response: string, dataSample: any[]) {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          chartType: parsed.chartType || 'bar',
          config: parsed.config || this.generateFallbackTemplateConfig(dataSample),
          dataMapping: parsed.dataMapping
        };
      }
    } catch (error) {
      console.warn("Failed to parse LLM response, using fallback");
    }
    
    return {
      chartType: 'bar',
      config: this.generateFallbackTemplateConfig(dataSample),
      dataMapping: null
    };
  }

  /**
   * 生成备用配置模板
   */
  private generateFallbackTemplateConfig(data: any[]) {
    if (!data || data.length === 0) {
      return {
        title: { 
          text: 'No Data Available',
          left: 'center'
        },
        tooltip: { 
          trigger: 'axis' 
        },
        legend: { 
          data: ['No Data'],
          bottom: '0%'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: "__CATEGORIES_PLACEHOLDER__",
          name: 'Category'
        },
        yAxis: {
          type: 'value',
          name: 'Value'
        },
        series: [{ 
          name: 'No Data', 
          type: 'bar',
          data: "__DATA_PLACEHOLDER__" 
        }]
      };
    }

    const columns = Object.keys(data[0]);
    const xColumn = columns[0];
    const yColumn = columns.find(col => typeof data[0][col] === 'number') || columns[1] || columns[0];
    
    return {
      title: {
        text: 'Data Visualization',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: { 
        trigger: 'axis',
        backgroundColor: 'rgba(50,50,50,0.7)',
        borderColor: '#777',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        }
      },
      legend: { 
        data: [yColumn],
        bottom: '0%'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: "__CATEGORIES_PLACEHOLDER__",
        name: xColumn,
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: 14
        }
      },
      yAxis: {
        type: 'value',
        name: yColumn,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 14
        }
      },
      series: [{
        name: yColumn,
        type: 'bar',
        data: "__DATA_PLACEHOLDER__",
        itemStyle: {
          color: '#5470c6'
        },
        label: {
          show: false
        }
      }]
    };
  }

  /**
   * Adds axis label truncation for vertical charts with long labels.
   */
  private addAxisLabelTruncation(config: any): any {
    const newConfig = JSON.parse(JSON.stringify(config));

    // Only apply to vertical charts with a category X-axis
    if (newConfig.xAxis?.type === 'category' && newConfig.yAxis?.type === 'value') {
        
        newConfig.xAxis.axisLabel = {
            ...newConfig.xAxis.axisLabel,
            // Store the function as a string to be "revived" in the HTML
            formatter: "function(value) { return value.length > 10 ? value.slice(0, 8) + '...' : value; }"
        };

        // Enhance the tooltip to show the full label on hover
        newConfig.tooltip = {
            ...newConfig.tooltip,
            trigger: 'axis',
            // This formatter ensures the full category name (param.name) is shown in the tooltip
            formatter: "function(params) { const param = params[0]; return `${param.name}<br/>${param.marker}${param.seriesName}: <strong>${param.value}</strong>`; }"
        };
    }
    
    return newConfig;
  }

  /**
   * 生成完整HTML，动态注入数据
   */
  private generateHTML(chartConfig: any, chartType: string, actualData?: any[], dataStructure?: any): string {
    // 动态注入实际数据
    let finalConfig = this.injectDataIntoConfig(chartConfig, actualData, dataStructure);
    
    // 智能添加标签截断逻辑
    finalConfig = this.addAxisLabelTruncation(finalConfig);

    const configStr = JSON.stringify(finalConfig, null, 2)
      // Revive stringified formatter functions back into real JavaScript functions
      .replace(/"(function\(.*?\}.*?)"/g, '$1'); 
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive ${chartType} Chart</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0; 
            padding: 20px; 
            background-color: #f8f9fa;
        }
        #container { 
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            width: 100%;
            max-width: 1200px;
            height: 400px;
            margin: 0 auto;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            text-align: center;
            padding: 40px;
            color: #d32f2f;
            background: #ffebee;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div class="loading">Loading chart...</div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script>
        try {
            const chartConfig = ${configStr};
            const chartContainer = document.getElementById('container');
            const myChart = echarts.init(chartContainer);
            
            // 配置图表响应式
            myChart.setOption(chartConfig);
            
            // 监听窗口大小变化
            window.addEventListener('resize', function() {
                myChart.resize();
            });
            
            console.log('Chart rendered successfully');
        } catch (error) {
            console.error('Chart rendering error:', error);
            document.getElementById('container').innerHTML = 
                '<div class="error">Error rendering chart: ' + error.message + '</div>';
        }
    </script>
</body>
</html>
  `;
  }

  /**
   * 动态注入实际数据到配置模板中
   */
  private injectDataIntoConfig(config: any, actualData?: any[], _dataStructure?: any): any {
    if (!actualData || actualData.length === 0) {
      return config;
    }

    // 深拷贝配置以避免修改原始配置
    const finalConfig = JSON.parse(JSON.stringify(config));

    // 分析数据结构
    const columns = Object.keys(actualData[0]);
    const xColumn = columns[0];
    const yColumn = columns.find(col => typeof actualData[0][col] === 'number') || columns[1] || columns[0];

    // 替换占位符
    const configStr = JSON.stringify(finalConfig);
    let updatedConfigStr = configStr;

    // 替换xAxis categories占位符 (ECharts使用xAxis.data)
    const categories = actualData.map(row => String(row[xColumn]));
    updatedConfigStr = updatedConfigStr.replace(
      '"__CATEGORIES_PLACEHOLDER__"',
      JSON.stringify(categories)
    );

    // 替换series data占位符
    const seriesData = actualData.map(row => {
      const value = row[yColumn];
      return typeof value === 'number' ? value : parseFloat(value) || 0;
    });
    updatedConfigStr = updatedConfigStr.replace(
      '"__DATA_PLACEHOLDER__"',
      JSON.stringify(seriesData)
    );

    return JSON.parse(updatedConfigStr);
  }
} 