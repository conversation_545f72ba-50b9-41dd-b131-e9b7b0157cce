import { Chat<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";
import { RunnableConfig } from "@langchain/core/runnables";
import { Text2SQLConfig } from "../config/text2sql.js";
import { Text2SQLError, TimeoutError } from "../agent/errors.js";
import { withTimeout } from "../agent/utils.js";
import { LLMResponse } from "../agent/types.js";

export class LLMService {
  private llm: ChatOpenAI;
  private config: Text2SQLConfig;

  constructor(config: Text2SQLConfig) {
    this.config = config;
    this.llm = this.createLLM();
  }

  private createLLM(): ChatOpenAI {
    return new ChatOpenAI({
      model: this.config.llm.model,
      temperature: this.config.llm.temperature,
      streaming: true,
      verbose: false,
      configuration: {
        apiKey: this.config.llm.apiKey || "",
        baseURL: this.config.llm.baseURL,
      },
    });
  }

  /**
   * Invoke LLM with system and human messages
   */
  public async invoke(
    systemPrompt: string,
    userPrompt: string,
    config?: RunnableConfig,
  ): Promise<LLMResponse> {
    if (!systemPrompt?.trim() || !userPrompt?.trim()) {
      throw new Text2SQLError(
        "Both system and user prompts are required",
        "INVALID_PROMPT",
      );
    }

    try {
      const messages = [
        new SystemMessage(systemPrompt),
        new HumanMessage(userPrompt),
      ];

      console.log(
        `[LLM Service] Invoking ${this.config.llm.model} with prompts`,
      );

      const response = await withTimeout(
        this.llm.invoke(messages, {
          callbacks: config?.callbacks,
        }),
        this.config.execution.timeoutMs,
        "LLM request timed out",
      );

      if (!response || !response.content) {
        throw new Text2SQLError(
          "LLM returned empty response",
          "EMPTY_RESPONSE",
        );
      }

      const content = response.content as string;
      console.log(
        `[LLM Service] ✅ Received response (${content.length} chars)`,
      );

      return {
        content: content.trim(),
        metadata: {
          model: this.config.llm.model,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      if (error instanceof Error && error.message.includes("timed out")) {
        throw new TimeoutError(
          `LLM request timed out after ${this.config.execution.timeoutMs}ms`,
          { model: this.config.llm.model },
        );
      }

      const errorMessage =
        error instanceof Error ? error.message : "Unknown LLM error";
      console.log(`[LLM Service] ❌ Request failed: ${errorMessage}`);

      throw new Text2SQLError(
        `LLM request failed: ${errorMessage}`,
        "LLM_REQUEST_FAILED",
        {
          model: this.config.llm.model,
          originalError: errorMessage,
        },
      );
    }
  }

  /**
   * Get LLM configuration info
   */
  public getConfig(): Text2SQLConfig["llm"] {
    return {
      ...this.config.llm,
      apiKey: this.config.llm.apiKey ? "[REDACTED]" : undefined,
    };
  }

  /**
   * Update LLM configuration
   */
  public updateConfig(newConfig: Partial<Text2SQLConfig["llm"]>): void {
    this.config.llm = { ...this.config.llm, ...newConfig };
    this.llm = this.createLLM();
    console.log(`[LLM Service] Configuration updated`);
  }
}
