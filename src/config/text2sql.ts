export interface Text2SQLConfig {
  llm: {
    model: string;
    temperature: number;
    apiKey?: string;
    baseURL?: string;
  };
  rag: {
    searchK: number;
    similarityThreshold: number;
    timeoutMs?: number;
  };
  execution: {
    maxRetries: number;
    timeoutMs: number;
    maxStepsPerPlan: number;
    recursionLimit: number;
  };
}

export const DEFAULT_TEXT2SQL_CONFIG: Text2SQLConfig = {
  llm: {
    model: "deepseek-v3",
    temperature: 0.1,
    baseURL: "https://api.lkeap.tencentcloud.com/v1",
  },
  rag: {
    searchK: 5,
    similarityThreshold: 0.4,
    timeoutMs: 30000, // Increased to 30 seconds for reliable RAG initialization
  },
  execution: {
    maxRetries: 2, // Reduced from 3 to limit token waste
    timeoutMs: 300000, // Match ClickHouse timeout (5 minutes)
    maxStepsPerPlan: 5, // Reduced from 10 to keep plans simpler
    recursionLimit: 5, // Prevent infinite loops in workflow
  },
};

export const getConfig = (): Text2SQLConfig => {
  return {
    ...DEFAULT_TEXT2SQL_CONFIG,
    llm: {
      ...DEFAULT_TEXT2SQL_CONFIG.llm,
      apiKey: process.env.DS_API_KEY || "",
    },
  };
};
