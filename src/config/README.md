# Database Schema Configuration

这个目录包含了项目的集中化数据库 schema 配置，旨在减少重复代码并提高维护性。

## 文件结构

- `database.ts` - 核心 schema 定义和常量
- `schemaFormatter.ts` - 格式化工具，用于生成不同格式的 schema 文本
- `text2sql.ts` - Text2SQL 系统的运行时配置

## 主要特性

### 1. 集中化 Schema 定义

所有数据库表的 schema 信息都集中在 `database.ts` 中：

```typescript
import { DATABASE_SCHEMA, TABLE_MAPPING, QUERY_RULES } from "./database.js";

// 访问表名
const dexTable = TABLE_MAPPING.DEX_TRADING;
const pumpfunTable = TABLE_MAPPING.PUMPFUN_TRADING;

// 访问查询规则
const defaultLimit = QUERY_RULES.DEFAULT_LIMIT;
```

### 2. 动态格式化

`schemaFormatter.ts` 提供多种格式化函数，用于生成不同用途的文本：

```typescript
import {
  formatSchemaForPrompt,
  formatTableSelectionRules,
} from "./schemaFormatter.js";

// 为 prompt 生成完整的 schema 文本
const promptSchema = formatSchemaForPrompt();

// 生成表选择规则
const selectionRules = formatTableSelectionRules();
```

### 3. 类型安全

所有配置都使用 TypeScript 的类型系统，确保编译时类型检查：

```typescript
// 类型安全的表名常量
const tableName: string = TABLE_MAPPING.DEX_TRADING;

// 完整的 schema 对象，符合 DatabaseSchema 接口
const schema: DatabaseSchema = DATABASE_SCHEMA;
```

## 使用场景

### 在 Prompt 中使用

```typescript
// 旧方式：硬编码 schema
const oldPrompt = `
Table Schema:
hubble.distributed_dex_token_trade_transaction
- type String COMMENT 'Transaction type'
...
`;

// 新方式：使用集中化配置
import { formatSchemaForPrompt } from "../config/schemaFormatter.js";

const newPrompt = `
Table Schema:
${formatSchemaForPrompt()}
`;
```

### 在业务逻辑中使用

```typescript
import { TABLE_MAPPING, FIELD_USAGE } from "../config/database.js";

// 构建 SQL 查询
const sql = `SELECT ${FIELD_USAGE.DEX_VOLUME.BUY}, ${FIELD_USAGE.DEX_VOLUME.SELL} 
             FROM ${TABLE_MAPPING.DEX_TRADING} 
             LIMIT ${QUERY_RULES.DEFAULT_LIMIT}`;
```

### 获取表信息

```typescript
import { getTableByName, getClickHouseTables } from "../config/database.js";

// 获取特定表的详细信息
const tableInfo = getTableByName(
  "hubble.distributed_dex_token_trade_transaction",
);
console.log(tableInfo?.columns);

// 获取所有 ClickHouse 表
const clickhouseTables = getClickHouseTables();
```

### UNION 查询的列匹配

```typescript
import { getCommonColumns } from "../config/schemaFormatter.js";

// 找到两个表的共同列，用于 UNION 查询
const commonCols = getCommonColumns([
  "hubble.distributed_dex_token_trade_transaction",
  "hubble.distributed_pumpfun_token_trade_transaction",
]);

const sql = `SELECT ${commonCols.join(", ")} FROM table1 
             UNION ALL 
             SELECT ${commonCols.join(", ")} FROM table2`;
```

## 迁移指南

### 从硬编码 Schema 迁移

1. **替换硬编码表名**：

   ```typescript
   // 之前
   const tableName = "hubble.distributed_dex_token_trade_transaction";

   // 之后
   import { TABLE_MAPPING } from "./config/database.js";
   const tableName = TABLE_MAPPING.DEX_TRADING;
   ```

2. **替换硬编码 Schema 文本**：

   ```typescript
   // 之前
   const prompt = `Schema: hubble.distributed_dex...`;

   // 之后
   import { formatSchemaForPrompt } from "./config/schemaFormatter.js";
   const prompt = `Schema: ${formatSchemaForPrompt()}`;
   ```

3. **替换硬编码字段名**：

   ```typescript
   // 之前
   const volumeField = "buy_sol_amount";

   // 之后
   import { FIELD_USAGE } from "./config/database.js";
   const volumeField = FIELD_USAGE.DEX_VOLUME.BUY;
   ```

## 维护指南

### 添加新表

1. 在 `database.ts` 的 `TABLES` 对象中添加新表定义
2. 在 `TABLE_MAPPING` 中添加便捷访问常量
3. 如需要，更新 `schemaFormatter.ts` 中的格式化逻辑

### 修改现有表

1. 在 `database.ts` 中更新对应表的 `columns` 数组
2. 检查是否需要更新 `FIELD_USAGE` 常量
3. 运行测试确保所有引用都正确更新

### 添加新的格式化函数

在 `schemaFormatter.ts` 中添加新的格式化函数，并在需要的地方导入使用。

## 最佳实践

1. **始终使用常量**：避免硬编码表名和字段名
2. **类型优先**：利用 TypeScript 的类型系统进行编译时检查
3. **集中管理**：所有 schema 相关的修改都应该在这个目录中进行
4. **测试覆盖**：确保 schema 变更有对应的测试覆盖

## 示例

查看 `../examples/schemaUsage.ts` 获取完整的使用示例。
