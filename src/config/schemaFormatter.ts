import {
  DATABASE_SCHEMA,
  TABLE_MAPPING,
  QUERY_RULES,
  FIELD_USAGE,
} from "./database.js";

/**
 * Format schema for text2sql prompt
 */
export const formatSchemaForPrompt = (): string => {
  let result = "";

  DATABASE_SCHEMA.tables.forEach((table) => {
    result += `${table.name}\n`;
    table.columns.forEach((column) => {
      result += `- ${column.name} ${column.type}`;
      if (column.description) {
        result += ` COMMENT '${column.description}'`;
      }
      result += "\n";
    });
    result += "\n";
  });

  return result;
};

/**
 * Format table selection rules
 */
export const formatTableSelectionRules = (): string => {
  return `Follow these rules to choose the correct table(s):

1.  **Default to DEX Trading**: For general trading questions, use \`${TABLE_MAPPING.DEX_TRADING}\`.
2.  **PumpFun Trading**: If the user mentions "${QUERY_RULES.PUMPFUN_KEYWORD}", use \`${TABLE_MAPPING.PUMPFUN_TRADING}\`.
3.  **Token Transfers (Non-Trading)**: For questions about airdrops, rewards, direct transfers, or transfer patterns, use \`${TABLE_MAPPING.DEX_TRANSFERS}\`.
4.  **Token Holders**: For questions about holders, wallet positions, distribution, or profitability, use \`${TABLE_MAPPING.TOKEN_HOLDERS}\` (with PostgreSQL syntax).
5.  **Time-Series/OHLCV**: For k-line or time-series analysis, use the appropriate \`hubble.*_ohlcv_*\` table (\`_second\`, \`_min\`, \`_hour\`, \`_day\`).
6.  **Token Search Priority**: When searching for a token, check the DEX table first. If no data is found, then check the PumpFun table.`;
};

/**
 * Format field usage notes
 */
export const formatFieldUsageNotes = (): string => {
  return `- **Volume on PumpFun**: Use \`${FIELD_USAGE.PUMPFUN_VOLUME.BUY}\` and \`${FIELD_USAGE.PUMPFUN_VOLUME.SELL}\`.
- **Volume on DEX**: Use \`${FIELD_USAGE.DEX_VOLUME.BUY}\` and \`${FIELD_USAGE.DEX_VOLUME.SELL}\`.
- **Timestamps**: \`${FIELD_USAGE.TIMESTAMP_FIELD}\` is a Unix timestamp (Int64). Use appropriate date functions for the database (e.g., \`toDateTime()\` in ClickHouse).`;
};

/**
 * Get common columns between tables for UNION operations
 */
export const getCommonColumns = (tableNames: string[]): string[] => {
  if (tableNames.length === 0) return [];

  const tables = tableNames
    .map((name) => DATABASE_SCHEMA.tables.find((t) => t.name === name))
    .filter(Boolean);

  if (tables.length === 0) return [];

  // Find intersection of column names
  const firstTableColumns = tables[0]!.columns.map((c) => c.name);

  return firstTableColumns.filter((columnName) =>
    tables.every((table) =>
      table!.columns.some((col) => col.name === columnName),
    ),
  );
};

/**
 * Format database configuration for other prompts
 */
export const formatDatabaseInfo = (): string => {
  return `**ClickHouse Database**: Contains hubble.* tables (trading, OHLCV, and transfer data)
**PostgreSQL Database**: Contains public.* tables (aggregated holder data)

**CRITICAL**: Always match the database syntax to the table being queried:
- hubble.* tables → ClickHouse syntax
- public.* tables → PostgreSQL syntax`;
};

/**
 * Format lightweight table info for planning prompt (no detailed columns)
 */
export const formatTableListForPlanning = (): string => {
  let result = "## Available Tables for Analysis:\n\n";

  result += "**ClickHouse Tables (hubble.*):**\n";
  result += `- Trading: ${TABLE_MAPPING.DEX_TRADING}, ${TABLE_MAPPING.PUMPFUN_TRADING}\n`;
  result += `- OHLCV: ${TABLE_MAPPING.DEX_OHLCV_DAY}, ${TABLE_MAPPING.PUMP_OHLCV_DAY}\n`;
  result += `- Transfers: ${TABLE_MAPPING.DEX_TRANSFERS}\n\n`;

  result += "**PostgreSQL Tables (public.*):**\n";
  result += `- Holders: ${TABLE_MAPPING.TOKEN_HOLDERS}\n\n`;

  result += "**Table Selection Rules:**\n";
  result += `- Use '${QUERY_RULES.PUMPFUN_KEYWORD}' keyword to detect Pump.fun queries\n`;
  result += `- Default limit: ${QUERY_RULES.DEFAULT_LIMIT} rows\n`;

  return result;
};

/**
 * Format compact table reference for SQL generation prompts
 */
export const formatCompactTableReference = (): string => {
  let result = "## Quick Table Reference\n\n";

  // Get core table mappings
  result += "**Core Tables:**\n";
  result += `- DEX Trading: \`${TABLE_MAPPING.DEX_TRADING}\`\n`;
  result += `- PumpFun Trading: \`${TABLE_MAPPING.PUMPFUN_TRADING}\`\n`;
  result += `- Token Holders: \`${TABLE_MAPPING.TOKEN_HOLDERS}\`\n`;
  result += `- DEX Transfers: \`${TABLE_MAPPING.DEX_TRANSFERS}\`\n`;
  result += `- OHLCV Tables: Available in second/min/hour/day timeframes\n\n`;

  return result;
};

/**
 * Get only table names (minimal exposure for planning)
 */
export const getTableNames = (): string[] => {
  return [
    TABLE_MAPPING.DEX_TRADING,
    TABLE_MAPPING.PUMPFUN_TRADING,
    TABLE_MAPPING.DEX_TRANSFERS,
    TABLE_MAPPING.TOKEN_HOLDERS,
    ...Object.values(TABLE_MAPPING).filter((name) => name.includes("ohlcv")),
  ];
};

/**
 * Check if query needs full schema (only for SQL generation)
 */
export const shouldUseFullSchema = (nodeType: string): boolean => {
  return nodeType === "executor" || nodeType === "sql_generation";
};

// Export only what's needed based on context
export { DATABASE_SCHEMA, TABLE_MAPPING, QUERY_RULES, FIELD_USAGE };
