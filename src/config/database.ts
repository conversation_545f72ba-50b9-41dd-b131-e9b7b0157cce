import { DatabaseSchema } from "../agent/state.js";

// Table definitions with complete schema information
export const TABLES = {
  // ClickHouse Tables
  PUMPFUN_TRADES: {
    name: "hubble.distributed_pumpfun_token_trade_transaction",
    type: "clickhouse" as const,
    description: "PumpFun token trading transactions",
    columns: [
      { name: "type", type: "String", description: "Transaction type" },
      {
        name: "trade_timestamp",
        type: "Int64",
        description: "Trade timestamp (s)",
      },
      {
        name: "transaction_slot",
        type: "String",
        description: "Transaction slot ID",
      },
      {
        name: "trader_wallet_address",
        type: "String",
        description: "Trader wallet address",
      },
      {
        name: "token_mint_address",
        type: "String",
        description: "Token mint address",
      },
      {
        name: "sol_price",
        type: "Nullable(Float64)",
        description: "SOL price",
      },
      {
        name: "signers",
        type: "Array(String)",
        description: "Signer addresses",
      },
      {
        name: "signer_balance_changes",
        type: "String",
        description: "Signer balance changes (JSON format)",
      },
      { name: "buy_amount", type: "Float64", description: "Buy amount" },
      {
        name: "buy_from_account_address",
        type: "String",
        description: "Buyer account address",
      },
      {
        name: "buy_to_account_address",
        type: "String",
        description: "Seller account address",
      },
      { name: "buy_solSpent", type: "Float64", description: "SOL spent" },
      {
        name: "buy_price_sol",
        type: "Float64",
        description: "Buy price (SOL denominated)",
      },
      {
        name: "buy_price_usd",
        type: "Float64",
        description: "Buy price (USD denominated)",
      },
      { name: "sell_amount", type: "Float64", description: "Sell amount" },
      {
        name: "sell_from_account_address",
        type: "String",
        description: "Seller account address",
      },
      {
        name: "sell_to_account_address",
        type: "String",
        description: "Buyer account address",
      },
      {
        name: "sell_solReceived",
        type: "Float64",
        description: "SOL received",
      },
      {
        name: "sell_price_sol",
        type: "Float64",
        description: "Sell price (SOL denominated)",
      },
      {
        name: "sell_price_usd",
        type: "Float64",
        description: "Sell price (USD denominated)",
      },
      {
        name: "arbitrage_profit_loss",
        type: "Float64",
        description: "Arbitrage profit or loss",
      },
      {
        name: "arbitrage_price_change",
        type: "Float64",
        description: "Arbitrage price change",
      },
      {
        name: "arbitrage_volume",
        type: "Float64",
        description: "Arbitrage volume",
      },
      {
        name: "pool_pre_reserves",
        type: "Float64",
        description: "Pool pre reserves",
      },
      {
        name: "poolState_realTokenReserves_postReserves",
        type: "Float64",
        description: "Pool post reserves",
      },
      {
        name: "poolState_realTokenReserves_change",
        type: "Float64",
        description: "Pool reserves change",
      },
      {
        name: "net_sol_balance_change",
        type: "Float64",
        description: "Net SOL balance change",
      },
      {
        name: "transaction_fee",
        type: "UInt32",
        description: "Transaction fee (lamports)",
      },
      {
        name: "transaction_signature",
        type: "String",
        description: "Transaction signature",
      },
      {
        name: "progress",
        type: "Float64",
        description: "Data processing progress",
      },
    ],
  },

  DEX_TRADES: {
    name: "hubble.distributed_dex_token_trade_transaction",
    type: "clickhouse" as const,
    description: "DEX token trading transactions",
    columns: [
      { name: "type", type: "String", description: "Transaction type" },
      {
        name: "trade_timestamp",
        type: "Int64",
        description: "Trade timestamp (s)",
      },
      {
        name: "transaction_slot",
        type: "String",
        description: "Transaction slot ID",
      },
      {
        name: "trader_wallet_address",
        type: "String",
        description: "Trader wallet address",
      },
      {
        name: "token_mint_address",
        type: "String",
        description: "Token mint address",
      },
      {
        name: "sol_price",
        type: "Nullable(Float64)",
        description: "SOL price",
      },
      {
        name: "signers",
        type: "Array(String)",
        description: "Signer addresses",
      },
      {
        name: "signer_balance_changes",
        type: "String",
        description: "Signer balance changes (JSON format)",
      },
      {
        name: "buy_currency",
        type: "Nullable(Int32)",
        description: "Buy currency type",
      },
      {
        name: "buy_amount",
        type: "Nullable(Float64)",
        description: "Buy amount",
      },
      {
        name: "buy_from_account_address",
        type: "Nullable(String)",
        description: "Buyer account address",
      },
      {
        name: "buy_to_account_address",
        type: "Nullable(String)",
        description: "Seller account address",
      },
      {
        name: "buy_price_sol",
        type: "Nullable(Float64)",
        description: "Buy price (SOL denominated)",
      },
      {
        name: "buy_sol_amount",
        type: "Nullable(Float64)",
        description: "Buy SOL amount (SOL denominated)",
      },
      {
        name: "sell_currency",
        type: "Nullable(Int32)",
        description: "Sell currency type",
      },
      {
        name: "sell_amount",
        type: "Nullable(Float64)",
        description: "Sell amount",
      },
      {
        name: "sell_from_account_address",
        type: "Nullable(String)",
        description: "Seller account address",
      },
      {
        name: "sell_to_account_address",
        type: "Nullable(String)",
        description: "Buyer account address",
      },
      {
        name: "sell_price_sol",
        type: "Nullable(Float64)",
        description: "Sell price (SOL denominated)",
      },
      {
        name: "sell_sol_amount",
        type: "Nullable(Float64)",
        description: "Sell SOL amount (SOL denominated)",
      },
      {
        name: "arbitrage_profit_loss",
        type: "Nullable(Float64)",
        description: "Arbitrage profit or loss",
      },
      {
        name: "arbitrage_price_change",
        type: "Nullable(Float64)",
        description: "Arbitrage price change",
      },
      {
        name: "arbitrage_volume",
        type: "Nullable(Float64)",
        description: "Arbitrage volume",
      },
      {
        name: "pool_state_real_token_reserves_pre_reserves",
        type: "Nullable(Float64)",
        description: "Pool real token reserves (pre)",
      },
      {
        name: "pool_state_real_token_reserves_post_reserves",
        type: "Nullable(Float64)",
        description: "Pool real token reserves (post)",
      },
      {
        name: "pool_state_real_token_reserves_change",
        type: "Nullable(Float64)",
        description: "Pool real token reserves change",
      },
      {
        name: "pool_state_completion_rate",
        type: "Nullable(Float64)",
        description: "Pool completion rate",
      },
      {
        name: "net_sol_balance_change",
        type: "Nullable(Float64)",
        description: "Net SOL balance change",
      },
      {
        name: "transaction_fee",
        type: "Nullable(UInt32)",
        description: "Transaction fee (lamports)",
      },
      {
        name: "transaction_signature",
        type: "Nullable(String)",
        description: "Transaction signature",
      },
      {
        name: "reason",
        type: "Nullable(String)",
        description: "Operation reason",
      },
      {
        name: "source_name",
        type: "Nullable(String)",
        description: "Data source name",
      },
      {
        name: "source_program",
        type: "Nullable(String)",
        description: "Source program",
      },
      {
        name: "version",
        type: "Nullable(String)",
        description: "Version number",
      },
    ],
  },

  DEX_TRANSFERS: {
    name: "hubble.distributed_dex_token_transfer_transaction",
    type: "clickhouse" as const,
    description: "DEX token transfer transactions (non-trading)",
    columns: [
      { name: "type", type: "String", description: "Transaction type" },
      {
        name: "trade_timestamp",
        type: "Int64",
        description: "Trade timestamp (s)",
      },
      {
        name: "transaction_slot",
        type: "String",
        description: "Transaction slot ID",
      },
      {
        name: "trader_wallet_address",
        type: "String",
        description: "Trader wallet address",
      },
      {
        name: "token_mint_address",
        type: "String",
        description: "Token mint address",
      },
      {
        name: "sol_price",
        type: "Nullable(Float64)",
        description: "SOL price",
      },
      {
        name: "signers",
        type: "Array(String)",
        description: "Signer addresses",
      },
      {
        name: "source_program",
        type: "Nullable(String)",
        description: "Source program",
      },
      {
        name: "source_name",
        type: "Nullable(String)",
        description: "Data source name",
      },
      {
        name: "signer_balance_changes",
        type: "String",
        description: "Signer balance changes (JSON format)",
      },
      {
        name: "token_transfer_flows",
        type: "String",
        description: "Token transfer flows (structured Tuple)",
      },
      {
        name: "token_transfer_pattern",
        type: "Nullable(String)",
        description: "Transfer pattern",
      },
      {
        name: "is_multi_sender",
        type: "Nullable(UInt8)",
        description: "Is multi-sender",
      },
      {
        name: "is_multi_receiver",
        type: "Nullable(UInt8)",
        description: "Is multi-receiver",
      },
      {
        name: "has_sol_transfer",
        type: "Nullable(UInt8)",
        description: "Has SOL transfer",
      },
      {
        name: "has_token_transfer",
        type: "Nullable(UInt8)",
        description: "Has Token transfer",
      },
      {
        name: "is_single_authority_distribution",
        type: "Nullable(UInt8)",
        description: "Is single authority distribution",
      },
      {
        name: "primary_receivers",
        type: "String",
        description: "Primary receivers (structured Tuple)",
      },
      {
        name: "non_zero_transfer_count",
        type: "Nullable(UInt32)",
        description: "Non-zero transfer count",
      },
      {
        name: "pool_state_real_token_reserves_pre_reserves",
        type: "Nullable(Float64)",
        description: "Pool real token reserves (pre)",
      },
      {
        name: "pool_state_real_token_reserves_post_reserves",
        type: "Nullable(Float64)",
        description: "Pool real token reserves (post)",
      },
      {
        name: "pool_state_real_token_reserves_change",
        type: "Nullable(Float64)",
        description: "Pool real token reserves change",
      },
      {
        name: "net_sol_balance_change",
        type: "Nullable(Float64)",
        description: "Net SOL balance change",
      },
      {
        name: "transaction_fee",
        type: "Nullable(UInt32)",
        description: "Transaction fee (lamports)",
      },
      {
        name: "transaction_signature",
        type: "Nullable(String)",
        description: "Transaction signature",
      },
      {
        name: "version",
        type: "Nullable(String)",
        description: "Version number",
      },
    ],
  },

  TOKEN_HOLDERS: {
    name: "public.token_holders",
    type: "postgresql" as const,
    description: "Token holder analysis data",
    columns: [
      {
        name: "token_ca",
        type: "String",
        description: "Token contract address",
      },
      {
        name: "wallet_addr",
        type: "String",
        description: "Holder wallet address",
      },
      {
        name: "first_buy_time",
        type: "DateTime",
        description: "First buy timestamp",
      },
      {
        name: "holding_ratio",
        type: "Float64",
        description: "Holding ratio percentage",
      },
      {
        name: "holders_num",
        type: "Float64",
        description: "Total token holdings",
      },
      { name: "token_source", type: "String", description: "Token source" },
      { name: "total_sold", type: "Float64", description: "Total tokens sold" },
      {
        name: "total_bought",
        type: "Float64",
        description: "Total tokens bought",
      },
      { name: "total_profit", type: "Float64", description: "Total profit" },
      {
        name: "last_operation_time",
        type: "DateTime",
        description: "Last operation timestamp",
      },
      {
        name: "max_bought",
        type: "Float64",
        description: "Maximum single buy amount",
      },
      {
        name: "max_sold",
        type: "Float64",
        description: "Maximum single sell amount",
      },
      {
        name: "avg_bought_price",
        type: "Decimal(38, 16)",
        description: "Average buy price",
      },
      {
        name: "avg_sold_price",
        type: "Decimal(38, 16)",
        description: "Average sell price",
      },
      {
        name: "buy_count",
        type: "Int32",
        description: "Buy transaction count",
      },
      {
        name: "sell_count",
        type: "Int32",
        description: "Sell transaction count",
      },
    ],
  },
} as const;

// OHLCV tables with shared schema
export const OHLCV_COLUMNS = [
  { name: "token", type: "String", description: "Token symbol" },
  { name: "time", type: "DateTime", description: "Timestamp" },
  {
    name: "open_sol",
    type: "Float64",
    description: "Open price denominated in SOL",
  },
  {
    name: "high_sol",
    type: "Float64",
    description: "High price denominated in SOL",
  },
  {
    name: "low_sol",
    type: "Float64",
    description: "Low price denominated in SOL",
  },
  {
    name: "close_sol",
    type: "Float64",
    description: "Close price denominated in SOL",
  },
  {
    name: "volume_sol",
    type: "Float64",
    description: "The trading volume denominated in SOL",
  },
  {
    name: "open_usd",
    type: "Float64",
    description: "Open price denominated in USD",
  },
  {
    name: "high_usd",
    type: "Float64",
    description: "High price denominated in USD",
  },
  {
    name: "low_usd",
    type: "Float64",
    description: "Low price denominated in USD",
  },
  {
    name: "close_usd",
    type: "Float64",
    description: "Close price denominated in USD",
  },
  {
    name: "volume_usd",
    type: "Float64",
    description: "The trading volume denominated in USD",
  },
  { name: "count", type: "Int64", description: "Trade count" },
  {
    name: "progress",
    type: "Float64",
    description: "Data processing progress",
  },
  { name: "is_validated", type: "UInt8", description: "Is validated" },
];

// OHLCV table definitions
export const OHLCV_TABLES = [
  "hubble.distributed_pump_ohlcv_second",
  "hubble.distributed_pump_ohlcv_min",
  "hubble.distributed_pump_ohlcv_hour",
  "hubble.distributed_pump_ohlcv_day",
  "hubble.distributed_dex_ohlcv_second",
  "hubble.distributed_dex_ohlcv_min",
  "hubble.distributed_dex_ohlcv_hour",
  "hubble.distributed_dex_ohlcv_day",
] as const;

// Complete database schema
export const DATABASE_SCHEMA: DatabaseSchema = {
  tables: [
    {
      ...TABLES.PUMPFUN_TRADES,
      columns: [...TABLES.PUMPFUN_TRADES.columns],
    },
    {
      ...TABLES.DEX_TRADES,
      columns: [...TABLES.DEX_TRADES.columns],
    },
    {
      ...TABLES.DEX_TRANSFERS,
      columns: [...TABLES.DEX_TRANSFERS.columns],
    },
    {
      ...TABLES.TOKEN_HOLDERS,
      columns: [...TABLES.TOKEN_HOLDERS.columns],
    },
    // Add OHLCV tables
    ...OHLCV_TABLES.map((tableName) => ({
      name: tableName,
      type: "clickhouse" as const,
      description: `OHLCV data for ${tableName.includes("pump") ? "PumpFun" : "DEX"} ${tableName.split("_").pop()} timeframe`,
      columns: [...OHLCV_COLUMNS],
    })),
  ],
};

// Helper functions
export const getTableByName = (tableName: string) => {
  return DATABASE_SCHEMA.tables.find((table) => table.name === tableName);
};

export const getClickHouseTables = () => {
  return DATABASE_SCHEMA.tables.filter((table) => table.type === "clickhouse");
};

export const getPostgreSQLTables = () => {
  return DATABASE_SCHEMA.tables.filter((table) => table.type === "postgresql");
};

// Table mapping for easy access
export const TABLE_MAPPING = {
  DEX_TRADING: TABLES.DEX_TRADES.name,
  PUMPFUN_TRADING: TABLES.PUMPFUN_TRADES.name,
  DEX_TRANSFERS: TABLES.DEX_TRANSFERS.name,
  TOKEN_HOLDERS: TABLES.TOKEN_HOLDERS.name,

  // OHLCV shortcuts
  DEX_OHLCV_SECOND: "hubble.distributed_dex_ohlcv_second",
  DEX_OHLCV_MIN: "hubble.distributed_dex_ohlcv_min",
  DEX_OHLCV_HOUR: "hubble.distributed_dex_ohlcv_hour",
  DEX_OHLCV_DAY: "hubble.distributed_dex_ohlcv_day",

  PUMP_OHLCV_SECOND: "hubble.distributed_pump_ohlcv_second",
  PUMP_OHLCV_MIN: "hubble.distributed_pump_ohlcv_min",
  PUMP_OHLCV_HOUR: "hubble.distributed_pump_ohlcv_hour",
  PUMP_OHLCV_DAY: "hubble.distributed_pump_ohlcv_day",
} as const;

// Query logic constants
export const QUERY_RULES = {
  DEFAULT_TRADING_TABLE: TABLE_MAPPING.DEX_TRADING,
  PUMPFUN_KEYWORD: "pumpfun",
  DEFAULT_LIMIT: 50,
} as const;

// Field usage mapping
export const FIELD_USAGE = {
  PUMPFUN_VOLUME: {
    BUY: "buy_solSpent",
    SELL: "sell_solReceived",
  },
  DEX_VOLUME: {
    BUY: "buy_sol_amount",
    SELL: "sell_sol_amount",
  },
  TIMESTAMP_FIELD: "trade_timestamp",
} as const;

export { type DatabaseSchema } from "../agent/state.js";
